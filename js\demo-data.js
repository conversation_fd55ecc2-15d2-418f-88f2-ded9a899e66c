/**
 * بيانات تجريبية لعرض النظام
 * يمكن استخدام هذا الملف لتحميل بيانات تجريبية للاختبار
 */

class DemoDataManager {
    constructor() {
        this.demoData = this.generateDemoData();
    }

    /**
     * توليد بيانات تجريبية شاملة
     */
    generateDemoData() {
        return {
            students: this.generateDemoStudents(),
            courses: this.generateDemoCourses(),
            dailyRecords: this.generateDemoDailyRecords(),
            users: this.generateDemoUsers()
        };
    }

    /**
     * توليد طلاب تجريبيين
     */
    generateDemoStudents() {
        const names = [
            'أحمد محمد العلي', 'محمد عبدالله السالم', 'عبدالرحمن خالد النصر',
            'فهد سعد الحربي', 'سلطان عبدالعزيز القحطاني', 'عبدالله أحمد الزهراني',
            'خالد محمد الغامدي', 'سعد عبدالرحمن العتيبي', 'عبدالعزيز فهد الدوسري',
            'محمد سلطان الشهري', 'أحمد عبدالله المطيري', 'فيصل محمد العنزي',
            'عبدالرحمن سعد القرني', 'سلمان خالد الحربي', 'عبدالله فهد الرشيد'
        ];

        const ranks = ['جندي', 'جندي أول', 'عريف', 'وكيل رقيب', 'رقيب'];
        const courses = [
            { code: 'INF', number: '001', name: 'دورة المشاة الأساسية' },
            { code: 'ARM', number: '002', name: 'دورة المدرعات المتقدمة' },
            { code: 'SIG', number: '003', name: 'دورة الإشارة والاتصالات' },
            { code: 'ENG', number: '004', name: 'دورة المهندسين العسكريين' },
            { code: 'LOG', number: '005', name: 'دورة الإمداد والتموين' }
        ];

        return names.map((name, index) => {
            const course = courses[index % courses.length];
            return {
                id: `demo_student_${index + 1}`,
                name: name,
                militaryNumber: `MIL${String(1000 + index).padStart(6, '0')}`,
                rank: ranks[index % ranks.length],
                courseCode: course.code,
                courseNumber: course.number,
                courseName: course.name,
                createdAt: new Date(2024, 0, 15 + index).toISOString(),
                updatedAt: new Date(2024, 0, 15 + index).toISOString()
            };
        });
    }

    /**
     * توليد دورات تجريبية
     */
    generateDemoCourses() {
        return [
            {
                id: 'demo_course_001',
                courseCode: 'INF',
                courseNumber: '001',
                courseName: 'دورة المشاة الأساسية',
                startDate: '2024-01-15',
                endDate: '2024-04-15',
                createdAt: '2024-01-10T08:00:00.000Z',
                updatedAt: '2024-01-10T08:00:00.000Z'
            },
            {
                id: 'demo_course_002',
                courseCode: 'ARM',
                courseNumber: '002',
                courseName: 'دورة المدرعات المتقدمة',
                startDate: '2024-02-01',
                endDate: '2024-05-01',
                createdAt: '2024-01-25T08:00:00.000Z',
                updatedAt: '2024-01-25T08:00:00.000Z'
            },
            {
                id: 'demo_course_003',
                courseCode: 'SIG',
                courseNumber: '003',
                courseName: 'دورة الإشارة والاتصالات',
                startDate: '2024-03-01',
                endDate: '2024-06-01',
                createdAt: '2024-02-20T08:00:00.000Z',
                updatedAt: '2024-02-20T08:00:00.000Z'
            },
            {
                id: 'demo_course_004',
                courseCode: 'ENG',
                courseNumber: '004',
                courseName: 'دورة المهندسين العسكريين',
                startDate: '2024-04-01',
                endDate: '2024-07-01',
                createdAt: '2024-03-15T08:00:00.000Z',
                updatedAt: '2024-03-15T08:00:00.000Z'
            },
            {
                id: 'demo_course_005',
                courseCode: 'LOG',
                courseNumber: '005',
                courseName: 'دورة الإمداد والتموين',
                startDate: '2024-05-01',
                endDate: '2024-08-01',
                createdAt: '2024-04-10T08:00:00.000Z',
                updatedAt: '2024-04-10T08:00:00.000Z'
            }
        ];
    }

    /**
     * توليد سجلات يومية تجريبية
     */
    generateDemoDailyRecords() {
        const supervisors = [
            'الرائد أحمد السعيد',
            'النقيب محمد الزهراني',
            'الملازم أول عبدالله الغامدي',
            'النقيب فهد العتيبي'
        ];

        const violations = [
            'عدم ارتداء الزي الرسمي',
            'التأخير عن المحاضرة',
            'عدم أداء التحية العسكرية',
            'استخدام الهاتف أثناء المحاضرة',
            'عدم إحضار الأدوات المطلوبة'
        ];

        const absenceTypes = [
            'غياب بعذر مرضي',
            'غياب بدون عذر',
            'إجازة رسمية',
            'غياب بعذر طارئ'
        ];

        const records = [];
        const students = this.generateDemoStudents();

        // توليد سجلات لآخر 30 يوم
        for (let i = 0; i < 30; i++) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            
            // تخطي عطل نهاية الأسبوع
            if (date.getDay() === 5 || date.getDay() === 6) continue;

            const recordStudents = students.slice(0, Math.floor(Math.random() * 8) + 3).map(student => {
                const hasViolation = Math.random() < 0.3; // 30% احتمال وجود مخالفة
                const hasDelay = Math.random() < 0.2; // 20% احتمال تأخير
                const hasAbsence = Math.random() < 0.1; // 10% احتمال غياب

                return {
                    name: student.name,
                    militaryNumber: student.militaryNumber,
                    rank: student.rank,
                    courseCode: student.courseCode,
                    courseNumber: student.courseNumber,
                    courseName: student.courseName,
                    violations: hasViolation ? [violations[Math.floor(Math.random() * violations.length)]] : [],
                    delays: hasDelay ? Math.floor(Math.random() * 3) + 1 : 0,
                    absenceType: hasAbsence ? absenceTypes[Math.floor(Math.random() * absenceTypes.length)] : '',
                    absenceHours: hasAbsence ? Math.floor(Math.random() * 4) + 1 : 0
                };
            });

            records.push({
                id: `demo_record_${i + 1}`,
                date: date.toISOString().split('T')[0],
                supervisor: supervisors[Math.floor(Math.random() * supervisors.length)],
                students: recordStudents,
                attachments: [],
                createdAt: date.toISOString(),
                updatedAt: date.toISOString()
            });
        }

        return records;
    }

    /**
     * توليد مستخدمين تجريبيين
     */
    generateDemoUsers() {
        return [
            {
                id: 1,
                name: 'المشرف العام',
                username: 'admin',
                role: 'admin',
                permissions: ['all']
            },
            {
                id: 2,
                name: 'الرائد أحمد السعيد',
                username: 'major_ahmed',
                role: 'supervisor',
                permissions: ['add_daily', 'view_records']
            },
            {
                id: 3,
                name: 'النقيب محمد الزهراني',
                username: 'captain_mohammed',
                role: 'supervisor',
                permissions: ['add_daily', 'view_records']
            },
            {
                id: 4,
                name: 'الملازم أول عبدالله الغامدي',
                username: 'lieutenant_abdullah',
                role: 'officer',
                permissions: ['view_records']
            }
        ];
    }

    /**
     * تحميل البيانات التجريبية في النظام
     */
    loadDemoData() {
        try {
            // دمج البيانات التجريبية مع البيانات الموجودة
            const currentData = dataManager.data;
            
            // إضافة الطلاب التجريبيين
            this.demoData.students.forEach(student => {
                if (!currentData.students.find(s => s.militaryNumber === student.militaryNumber)) {
                    currentData.students.push(student);
                }
            });

            // إضافة الدورات التجريبية
            this.demoData.courses.forEach(course => {
                if (!currentData.courses.find(c => c.courseCode === course.courseCode && c.courseNumber === course.courseNumber)) {
                    currentData.courses.push(course);
                }
            });

            // إضافة السجلات التجريبية
            this.demoData.dailyRecords.forEach(record => {
                if (!currentData.dailyRecords.find(r => r.date === record.date && r.supervisor === record.supervisor)) {
                    currentData.dailyRecords.push(record);
                }
            });

            // إضافة المستخدمين التجريبيين
            this.demoData.users.forEach(user => {
                if (!currentData.users.find(u => u.username === user.username)) {
                    currentData.users.push(user);
                }
            });

            // حفظ البيانات
            dataManager.saveData();
            
            return true;
        } catch (error) {
            console.error('خطأ في تحميل البيانات التجريبية:', error);
            return false;
        }
    }

    /**
     * مسح البيانات التجريبية
     */
    clearDemoData() {
        try {
            const currentData = dataManager.data;
            
            // إزالة البيانات التجريبية
            currentData.students = currentData.students.filter(s => !s.id.startsWith('demo_'));
            currentData.courses = currentData.courses.filter(c => !c.id.startsWith('demo_'));
            currentData.dailyRecords = currentData.dailyRecords.filter(r => !r.id.startsWith('demo_'));
            currentData.users = currentData.users.filter(u => u.id === 1); // الاحتفاظ بالمشرف العام فقط

            dataManager.saveData();
            return true;
        } catch (error) {
            console.error('خطأ في مسح البيانات التجريبية:', error);
            return false;
        }
    }

    /**
     * إنشاء تقرير إحصائي للبيانات التجريبية
     */
    generateDemoReport() {
        const data = this.demoData;
        
        return {
            totalStudents: data.students.length,
            totalCourses: data.courses.length,
            totalRecords: data.dailyRecords.length,
            totalViolations: data.dailyRecords.reduce((sum, record) => 
                sum + record.students.reduce((studentSum, student) => 
                    studentSum + student.violations.length, 0), 0),
            totalDelays: data.dailyRecords.reduce((sum, record) => 
                sum + record.students.reduce((studentSum, student) => 
                    studentSum + student.delays, 0), 0),
            totalAbsences: data.dailyRecords.reduce((sum, record) => 
                sum + record.students.reduce((studentSum, student) => 
                    studentSum + student.absenceHours, 0), 0)
        };
    }
}

// إنشاء مثيل عام لإدارة البيانات التجريبية
window.demoDataManager = new DemoDataManager();

// تحميل البيانات التجريبية تلقائياً عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // التحقق من وجود بيانات، وإذا لم توجد، تحميل البيانات التجريبية
    if (dataManager.data.students.length === 0) {
        const success = demoDataManager.loadDemoData();
        if (success) {
            console.log('تم تحميل البيانات التجريبية بنجاح');
            
            // إعادة تحميل الإحصائيات إذا كان التطبيق محمل
            if (window.app) {
                setTimeout(() => {
                    app.loadDashboardStats();
                }, 1000);
            }
        }
    }
});
