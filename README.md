# نظام أرشفة اليوميات العسكرية - الإصدار 2.0 🎖️
## 📋 نظرة عامة
نظام شامل ومتطور لإدارة وأرشفة يوميات طلاب الدورات العسكرية، مصمم للعمل كتطبيق ويب محلي متقدم بدون الحاجة لاستضافة أو اتصال بالإنترنت. يتضمن الإصدار الجديد ميزات متطورة مثل المسح الضوئي، التقارير المتقدمة، وإدارة المستخدمين.

## 🆕 الجديد في الإصدار 2.0

### 🔐 إدارة المستخدمين المتقدمة
- **نظام مستخدمين كامل** مع أدوار وصلاحيات (مشرف عام، مراقب، ضابط)
- **إدارة الصلاحيات** المرنة لكل نوع مستخدم
- **حماية المستخدم الرئيسي** من التعديل أو الحذف

### 📷 نظام المسح الضوئي
- **مسح ضوئي مباشر** باستخدام كاميرا الجهاز
- **تحسين تلقائي للصور** (تباين، سطوع، وضوح)
- **إعدادات جودة متقدمة** مع دعم عدة تنسيقات
- **فلاش قابل للتحكم** وتبديل الكاميرات

### 🖼️ عارض المرفقات المتطور
- **تكبير وتصغير متقدم** مع دوران الصور
- **سحب للتحريك** في الصور المكبرة
- **معاينة ملء الشاشة** مع اختصارات لوحة المفاتيح
- **دعم أنواع ملفات متعددة** مع معاينة مصغرة

### 📊 نظام التقارير الشامل
- **7 أنواع تقارير مختلفة** (يومي، أسبوعي، شهري، طالب، دورة، مخالفات، حضور)
- **تصدير للطباعة** مع تصميم احترافي عربي
- **فلترة متقدمة** وإحصائيات تفصيلية

### 💾 النسخ الاحتياطي المتطور
- **نسخ احتياطي تلقائي** قابل للجدولة
- **إدارة شاملة للنسخ** مع واجهة مرئية
- **استعادة آمنة** مع تأكيدات متعددة

### ⚙️ صفحة الإعدادات الشاملة
- **تخصيص كامل** لأنواع المخالفات والغياب والرتب
- **إعدادات المظهر** مع ثيمات ألوان متعددة
- **أدوات صيانة متقدمة** للنظام

## ✨ المميزات الرئيسية
### 🎯 الواجهة والتصميم
- **دعم كامل للغة العربية** مع الكتابة من اليمين إلى اليسار (RTL)
- **تصميم عسكري احترافي** بألوان هادئة (كحلي، رصاصي، ذهبي)
- **تأثيرات حركية بسيطة** تجعل الواجهة حيوية ومتفاعلة
- **تصميم متجاوب** يعمل على جميع أحجام الشاشات
- **أيقونات عالية الجودة** لكل قسم من أقسام النظام

### 📝 إدارة اليوميات
- **نموذج يومية شامل** مع جميع الحقول المطلوبة
- **بحث تلقائي للطلاب** بالاسم أو الرقم العسكري
- **استيراد تلقائي لبيانات الدورات** عند إدخال رمز ورقم الدورة
- **إدارة المخالفات** مع قائمة منسدلة للأنواع المختلفة
- **تسجيل التأخيرات والغيابات** مع التحقق من الحدود المسموحة
- **نظام المرفقات** لتحميل المستندات الداعمة

### 🔍 البحث والفلترة
- **سجل اليوميات السابقة** مع إمكانية البحث والفلترة
- **فلترة بالتاريخ والمشرف واسم الطالب**
- **عرض تفصيلي لكل يومية** في نافذة منبثقة

### 📊 الإحصائيات والتقارير
- **لوحة تحكم تفاعلية** مع إحصائيات شاملة
- **عدادات للمخالفات والتأخيرات والغيابات**
- **إجمالي عدد الطلاب المسجلين**

### 🔔 نظام التنبيهات
- **إشعارات فورية** عند إضافة مخالفة أو تسجيل غياب
- **بانرات تنبيه ملونة** أعلى الصفحة للتنبيهات الهامة
- **نوافذ تأكيد** للعمليات الحساسة

## 🚀 طريقة التشغيل

### المتطلبات
- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- لا يتطلب اتصال بالإنترنت بعد التحميل الأولي

### خطوات التشغيل

1. **تحميل المشروع**
   ```
   قم بتحميل جميع ملفات المشروع في مجلد واحد
   ```

2. **فتح النظام**
   ```
   انقر نقراً مزدوجاً على ملف index.html
   أو
   اسحب الملف إلى نافذة المتصفح
   ```

3. **البدء في الاستخدام**
   - سيتم تحميل النظام تلقائياً
   - ستظهر رسالة ترحيب
   - يمكنك البدء في إضافة اليوميات فوراً

## 📁 هيكل المشروع

```
ارشفة-اليوميات-2/
├── index.html                 # الصفحة الرئيسية
├── css/
│   ├── main.css              # التصميم الرئيسي
│   ├── animations.css        # التأثيرات الحركية
│   └── rtl.css              # دعم اللغة العربية
├── js/
│   ├── main.js              # الوظائف الرئيسية
│   ├── data-manager.js      # إدارة البيانات
│   ├── notifications.js    # نظام التنبيهات
│   └── forms.js            # إدارة النماذج
├── assets/
│   ├── images/             # الصور والأيقونات
│   └── icons/              # أيقونات النظام
├── data/
│   ├── data.json           # قاعدة البيانات المحلية
│   └── attachments/        # المرفقات المرفوعة
└── README.md               # دليل الاستخدام
```

## 🎮 دليل الاستخدام

### 1. الصفحة الرئيسية
- **لوحة التحكم**: عرض الإحصائيات العامة
- **الإجراءات السريعة**: أزرار للوصول السريع للوظائف الرئيسية
- **القائمة الجانبية**: التنقل بين أقسام النظام

### 2. إضافة يومية جديدة
1. انقر على "إضافة اليومية" من القائمة الجانبية
2. أدخل تاريخ اليومية واسم المشرف
3. أضف بيانات الطلاب:
   - الاسم الكامل (مع البحث التلقائي)
   - الرقم العسكري
   - الرتبة
   - بيانات الدورة
   - المخالفات والتأخيرات والغيابات
4. أضف المرفقات إذا لزم الأمر
5. احفظ اليومية

### 3. إدارة الدورات
1. انقر على "إدارة الدورات" من القائمة الجانبية
2. أضف دورة جديدة بإدخال:
   - رمز الدورة
   - رقم الدورة
   - اسم الدورة
   - تاريخ البداية والنهاية

### 4. عرض السجلات السابقة
1. انقر على "سجل اليوميات السابقة"
2. استخدم الفلاتر للبحث:
   - بالتاريخ
   - باسم المشرف
   - باسم الطالب
3. انقر على أي سجل لعرض التفاصيل

## 💾 إدارة البيانات

### التخزين المحلي
- يستخدم النظام `localStorage` لحفظ البيانات محلياً
- البيانات محفوظة في متصفحك ولا تحتاج لخادم
- يمكن تصدير واستيراد البيانات عند الحاجة

### النسخ الاحتياطي
- انسخ ملف `data/data.json` بانتظام
- يمكن استيراد البيانات من ملف JSON
- احتفظ بنسخة احتياطية من مجلد المرفقات

## 🔧 التخصيص والإعدادات

### تخصيص أنواع المخالفات
يمكن تعديل قائمة المخالفات في ملف `data/data.json`:
```json
"violationTypes": [
  "عدم ارتداء الزي الرسمي",
  "عدم الانضباط في الطابور",
  "مخالفة مخصصة جديدة"
]
```

### تخصيص أنواع الغياب
```json
"absenceTypes": [
  "غياب بدون عذر",
  "غياب بعذر مرضي",
  "نوع غياب مخصص"
]
```

### تخصيص الرتب العسكرية
```json
"ranks": [
  "جندي",
  "عريف",
  "رتبة مخصصة"
]
```

## 🛠️ التقنيات المستخدمة

- **HTML5**: هيكل الصفحات
- **CSS3**: التصميم والتأثيرات الحركية
- **JavaScript ES6+**: المنطق والتفاعل
- **Bootstrap 5**: إطار العمل للتصميم المتجاوب
- **Font Awesome**: الأيقونات
- **Google Fonts**: خط Cairo للعربية

## 🔒 الأمان والخصوصية

- **لا يتطلب اتصال بالإنترنت** بعد التحميل الأولي
- **البيانات محفوظة محلياً** في جهازك فقط
- **لا يتم إرسال أي بيانات** لخوادم خارجية
- **تشفير البيانات الحساسة** (اختياري)

## 📞 الدعم والمساعدة

### المشاكل الشائعة

**المشكلة**: النظام لا يحفظ البيانات
**الحل**: تأكد من تمكين JavaScript في المتصفح

**المشكلة**: التصميم لا يظهر بشكل صحيح
**الحل**: تأكد من وجود اتصال بالإنترنت لتحميل Bootstrap و Font Awesome

**المشكلة**: الخطوط العربية لا تظهر
**الحل**: تأكد من تحميل خط Cairo من Google Fonts

### نصائح للاستخدام الأمثل

1. **استخدم متصفح حديث** للحصول على أفضل أداء
2. **احتفظ بنسخ احتياطية** من البيانات بانتظام
3. **تأكد من دقة البيانات** قبل الحفظ
4. **استخدم البحث التلقائي** لتوفير الوقت
5. **راجع الإحصائيات** بانتظام لمتابعة الأداء

## 📈 التطوير المستقبلي

- إضافة نظام التقارير المتقدمة
- تطوير نظام الصلاحيات
- إضافة خاصية الطباعة
- تطوير تطبيق الهاتف المحمول
- إضافة نظام النسخ الاحتياطي التلقائي

---

**تم تطوير هذا النظام خصيصاً للاستخدام العسكري مع مراعاة أعلى معايير الأمان والخصوصية**
