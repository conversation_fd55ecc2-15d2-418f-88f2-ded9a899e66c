/**
 * نظام التقارير للأرشفة العسكرية
 * يدير إنشاء وتصدير وطباعة التقارير المختلفة
 */

class ReportsManager {
    constructor() {
        this.reportTypes = {
            daily: 'تقرير يومي',
            weekly: 'تقرير أسبوعي',
            monthly: 'تقرير شهري',
            student: 'تقرير طالب',
            course: 'تقرير دورة',
            violations: 'تقرير المخالفات',
            attendance: 'تقرير الحضور والغياب'
        };
    }

    /**
     * فتح نافذة التقارير
     */
    openReportsModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fade reports-modal';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-chart-bar"></i> إنشاء التقارير
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="report-types">
                                    <h6><i class="fas fa-list"></i> أنواع التقارير</h6>
                                    <div class="list-group">
                                        ${Object.entries(this.reportTypes).map(([key, name]) => `
                                            <button class="list-group-item list-group-item-action report-type-btn"
                                                    data-type="${key}">
                                                <i class="fas fa-${this.getReportIcon(key)}"></i>
                                                ${name}
                                            </button>
                                        `).join('')}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="report-config" id="reportConfig">
                                    <div class="text-center text-muted py-5">
                                        <i class="fas fa-chart-bar fa-3x mb-3"></i>
                                        <h5>اختر نوع التقرير</h5>
                                        <p>حدد نوع التقرير من القائمة الجانبية لبدء الإعداد</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                        <button type="button" class="btn btn-primary" id="generateReportBtn" disabled>
                            <i class="fas fa-file-alt"></i> إنشاء التقرير
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);

        this.setupReportsEvents(modal);

        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });

        bootstrapModal.show();
    }

    /**
     * إعداد أحداث التقارير
     */
    setupReportsEvents(modal) {
        // اختيار نوع التقرير
        modal.querySelectorAll('.report-type-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                // إزالة التحديد السابق
                modal.querySelectorAll('.report-type-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');

                const reportType = btn.dataset.type;
                this.showReportConfig(reportType, modal);

                modal.querySelector('#generateReportBtn').disabled = false;
            });
        });

        // إنشاء التقرير
        modal.querySelector('#generateReportBtn').addEventListener('click', () => {
            this.generateReport(modal);
        });
    }

    /**
     * عرض إعدادات التقرير
     */
    showReportConfig(reportType, modal) {
        const configContainer = modal.querySelector('#reportConfig');

        switch (reportType) {
            case 'daily':
                configContainer.innerHTML = this.createDailyReportConfig();
                break;
            case 'weekly':
                configContainer.innerHTML = this.createWeeklyReportConfig();
                break;
            case 'monthly':
                configContainer.innerHTML = this.createMonthlyReportConfig();
                break;
            case 'student':
                configContainer.innerHTML = this.createStudentReportConfig();
                break;
            case 'course':
                configContainer.innerHTML = this.createCourseReportConfig();
                break;
            case 'violations':
                configContainer.innerHTML = this.createViolationsReportConfig();
                break;
            case 'attendance':
                configContainer.innerHTML = this.createAttendanceReportConfig();
                break;
        }
    }

    /**
     * إعداد التقرير اليومي
     */
    createDailyReportConfig() {
        return `
            <div class="report-config-content">
                <h6><i class="fas fa-calendar-day"></i> إعدادات التقرير اليومي</h6>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">التاريخ</label>
                            <input type="date" class="form-control" id="reportDate"
                                   value="${new Date().toISOString().split('T')[0]}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">المشرف</label>
                            <select class="form-control" id="reportSupervisor">
                                <option value="">جميع المشرفين</option>
                                ${this.getSupervisorOptions()}
                            </select>
                        </div>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">العناصر المطلوبة</label>
                    <div class="report-elements">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeStudents" checked>
                            <label class="form-check-label" for="includeStudents">قائمة الطلاب</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeViolations" checked>
                            <label class="form-check-label" for="includeViolations">المخالفات</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeAttendance" checked>
                            <label class="form-check-label" for="includeAttendance">الحضور والغياب</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeSummary" checked>
                            <label class="form-check-label" for="includeSummary">الملخص الإحصائي</label>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * إعداد التقرير الأسبوعي
     */
    createWeeklyReportConfig() {
        return `
            <div class="report-config-content">
                <h6><i class="fas fa-calendar-week"></i> إعدادات التقرير الأسبوعي</h6>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="weekStartDate">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="weekEndDate">
                        </div>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">الدورة</label>
                    <select class="form-control" id="reportCourse">
                        <option value="">جميع الدورات</option>
                        ${this.getCourseOptions()}
                    </select>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">نوع التحليل</label>
                    <div class="analysis-types">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="trendAnalysis" checked>
                            <label class="form-check-label" for="trendAnalysis">تحليل الاتجاهات</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="comparisonAnalysis" checked>
                            <label class="form-check-label" for="comparisonAnalysis">المقارنات</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="topViolators">
                            <label class="form-check-label" for="topViolators">أكثر الطلاب مخالفة</label>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * إعداد تقرير الطالب
     */
    createStudentReportConfig() {
        return `
            <div class="report-config-content">
                <h6><i class="fas fa-user-graduate"></i> إعدادات تقرير الطالب</h6>
                <div class="form-group mb-3">
                    <label class="form-label">اختيار الطالب</label>
                    <select class="form-control" id="selectedStudent" required>
                        <option value="">اختر الطالب</option>
                        ${this.getStudentOptions()}
                    </select>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="studentReportStartDate">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="studentReportEndDate"
                                   value="${new Date().toISOString().split('T')[0]}">
                        </div>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">التفاصيل المطلوبة</label>
                    <div class="student-details">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="studentInfo" checked>
                            <label class="form-check-label" for="studentInfo">المعلومات الأساسية</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="violationHistory" checked>
                            <label class="form-check-label" for="violationHistory">تاريخ المخالفات</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="attendanceRecord" checked>
                            <label class="form-check-label" for="attendanceRecord">سجل الحضور</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="performanceChart">
                            <label class="form-check-label" for="performanceChart">مخطط الأداء</label>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * إنشاء التقرير
     */
    generateReport(modal) {
        const activeBtn = modal.querySelector('.report-type-btn.active');
        if (!activeBtn) return;

        const reportType = activeBtn.dataset.type;
        const reportData = this.collectReportData(reportType, modal);

        if (!reportData) {
            notificationManager.showError('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        this.showLoading(true);

        setTimeout(() => {
            try {
                const report = this.buildReport(reportType, reportData);
                this.displayReport(report);
                bootstrap.Modal.getInstance(modal).hide();
                notificationManager.showSuccess('تم إنشاء التقرير بنجاح');
            } catch (error) {
                notificationManager.showError('حدث خطأ في إنشاء التقرير: ' + error.message);
            } finally {
                this.showLoading(false);
            }
        }, 1000);
    }

    /**
     * جمع بيانات التقرير
     */
    collectReportData(reportType, modal) {
        const data = { type: reportType };

        switch (reportType) {
            case 'daily':
                data.date = modal.querySelector('#reportDate').value;
                data.supervisor = modal.querySelector('#reportSupervisor').value;
                data.elements = {
                    students: modal.querySelector('#includeStudents').checked,
                    violations: modal.querySelector('#includeViolations').checked,
                    attendance: modal.querySelector('#includeAttendance').checked,
                    summary: modal.querySelector('#includeSummary').checked
                };
                break;

            case 'student':
                data.studentId = modal.querySelector('#selectedStudent').value;
                data.startDate = modal.querySelector('#studentReportStartDate').value;
                data.endDate = modal.querySelector('#studentReportEndDate').value;
                data.details = {
                    info: modal.querySelector('#studentInfo').checked,
                    violations: modal.querySelector('#violationHistory').checked,
                    attendance: modal.querySelector('#attendanceRecord').checked,
                    chart: modal.querySelector('#performanceChart').checked
                };

                if (!data.studentId) return null;
                break;

            // إضافة باقي أنواع التقارير...
        }

        return data;
    }

    /**
     * بناء التقرير
     */
    buildReport(reportType, data) {
        switch (reportType) {
            case 'daily':
                return this.buildDailyReport(data);
            case 'student':
                return this.buildStudentReport(data);
            // إضافة باقي أنواع التقارير...
            default:
                throw new Error('نوع التقرير غير مدعوم');
        }
    }

    /**
     * بناء التقرير اليومي
     */
    buildDailyReport(data) {
        const records = dataManager.getDailyRecordsByDate(data.date);
        const filteredRecords = data.supervisor ?
            records.filter(r => r.supervisor === data.supervisor) : records;

        let html = `
            <div class="report-header">
                <div class="report-title">
                    <h2>التقرير اليومي</h2>
                    <p>التاريخ: ${dataManager.formatDate(data.date)}</p>
                    ${data.supervisor ? `<p>المشرف: ${data.supervisor}</p>` : ''}
                </div>
                <div class="report-logo">
                    <i class="fas fa-shield-alt fa-3x"></i>
                </div>
            </div>
        `;

        if (data.elements.summary) {
            const stats = this.calculateDailyStats(filteredRecords);
            html += `
                <div class="report-section">
                    <h3>الملخص الإحصائي</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-number">${stats.totalStudents}</span>
                            <span class="stat-label">إجمالي الطلاب</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">${stats.totalViolations}</span>
                            <span class="stat-label">إجمالي المخالفات</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">${stats.totalDelays}</span>
                            <span class="stat-label">إجمالي التأخيرات</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">${stats.totalAbsences}</span>
                            <span class="stat-label">إجمالي الغيابات</span>
                        </div>
                    </div>
                </div>
            `;
        }

        if (data.elements.students && filteredRecords.length > 0) {
            html += `
                <div class="report-section">
                    <h3>تفاصيل الطلاب</h3>
                    <table class="report-table">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الرقم العسكري</th>
                                <th>الرتبة</th>
                                <th>الدورة</th>
                                ${data.elements.violations ? '<th>المخالفات</th>' : ''}
                                ${data.elements.attendance ? '<th>التأخيرات</th><th>الغيابات</th>' : ''}
                            </tr>
                        </thead>
                        <tbody>
                            ${filteredRecords.flatMap(record =>
                                record.students.map(student => `
                                    <tr>
                                        <td>${student.name}</td>
                                        <td>${student.militaryNumber}</td>
                                        <td>${student.rank}</td>
                                        <td>${student.courseName}</td>
                                        ${data.elements.violations ? `<td>${student.violations ? student.violations.join(', ') : '-'}</td>` : ''}
                                        ${data.elements.attendance ? `<td>${student.delays || 0}</td><td>${student.absenceHours || 0}</td>` : ''}
                                    </tr>
                                `)
                            ).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        return html;
    }

    /**
     * عرض التقرير
     */
    displayReport(reportHtml) {
        const reportWindow = window.open('', '_blank');
        reportWindow.document.write(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>تقرير النظام</title>
                <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
                <style>
                    ${this.getReportStyles()}
                </style>
            </head>
            <body>
                <div class="report-container">
                    ${reportHtml}
                    <div class="report-footer">
                        <div class="print-actions">
                            <button onclick="window.print()" class="btn btn-primary">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                            <button onclick="window.close()" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إغلاق
                            </button>
                        </div>
                        <div class="report-info">
                            <p>تم إنشاء التقرير في: ${new Date().toLocaleString('ar-SA')}</p>
                            <p>نظام أرشفة اليوميات العسكرية</p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
        `);
        reportWindow.document.close();
    }

    // الوظائف المساعدة
    getReportIcon(type) {
        const icons = {
            daily: 'calendar-day',
            weekly: 'calendar-week',
            monthly: 'calendar-alt',
            student: 'user-graduate',
            course: 'graduation-cap',
            violations: 'exclamation-triangle',
            attendance: 'clock'
        };
        return icons[type] || 'file-alt';
    }

    getSupervisorOptions() {
        const supervisors = [...new Set(dataManager.data.dailyRecords.map(r => r.supervisor))];
        return supervisors.map(s => `<option value="${s}">${s}</option>`).join('');
    }

    getCourseOptions() {
        return dataManager.data.courses.map(c =>
            `<option value="${c.id}">${c.courseName}</option>`
        ).join('');
    }

    getStudentOptions() {
        return dataManager.data.students.map(s =>
            `<option value="${s.id}">${s.name} - ${s.militaryNumber}</option>`
        ).join('');
    }

    calculateDailyStats(records) {
        return {
            totalStudents: records.reduce((sum, r) => sum + r.students.length, 0),
            totalViolations: records.reduce((sum, r) =>
                sum + r.students.reduce((s, st) => s + (st.violations ? st.violations.length : 0), 0), 0),
            totalDelays: records.reduce((sum, r) =>
                sum + r.students.reduce((s, st) => s + (st.delays || 0), 0), 0),
            totalAbsences: records.reduce((sum, r) =>
                sum + r.students.reduce((s, st) => s + (st.absenceHours || 0), 0), 0)
        };
    }

    getReportStyles() {
        return `
            body {
                font-family: 'Cairo', sans-serif;
                margin: 0;
                padding: 20px;
                background: #f8fafc;
                color: #2d3748;
                line-height: 1.6;
            }
            .report-container {
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                padding: 2rem;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }
            .report-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 3px solid #d69e2e;
                padding-bottom: 1rem;
                margin-bottom: 2rem;
            }
            .report-title h2 {
                color: #1a365d;
                margin: 0;
                font-size: 2rem;
                font-weight: 700;
            }
            .report-logo {
                color: #d69e2e;
            }
            .report-section {
                margin-bottom: 2rem;
            }
            .report-section h3 {
                color: #1a365d;
                border-bottom: 2px solid #e2e8f0;
                padding-bottom: 0.5rem;
                margin-bottom: 1rem;
            }
            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
                margin-bottom: 2rem;
            }
            .stat-item {
                text-align: center;
                padding: 1rem;
                background: #f7fafc;
                border-radius: 8px;
                border-top: 4px solid #d69e2e;
            }
            .stat-number {
                display: block;
                font-size: 2rem;
                font-weight: 700;
                color: #1a365d;
            }
            .stat-label {
                color: #4a5568;
                font-weight: 500;
            }
            .report-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 1rem;
            }
            .report-table th,
            .report-table td {
                padding: 0.75rem;
                text-align: right;
                border-bottom: 1px solid #e2e8f0;
            }
            .report-table th {
                background: #1a365d;
                color: white;
                font-weight: 600;
            }
            .report-table tbody tr:nth-child(even) {
                background: #f7fafc;
            }
            .report-footer {
                margin-top: 3rem;
                padding-top: 2rem;
                border-top: 2px solid #e2e8f0;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .btn {
                padding: 0.5rem 1rem;
                border: none;
                border-radius: 5px;
                font-weight: 600;
                cursor: pointer;
                margin-left: 0.5rem;
            }
            .btn-primary {
                background: #1a365d;
                color: white;
            }
            .btn-secondary {
                background: #4a5568;
                color: white;
            }
            .report-info {
                text-align: left;
                color: #718096;
                font-size: 0.9rem;
            }
            @media print {
                .print-actions {
                    display: none;
                }
                body {
                    background: white;
                }
                .report-container {
                    box-shadow: none;
                    padding: 0;
                }
            }
        `;
    }

    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = show ? 'flex' : 'none';
        }
    }
}

// إنشاء مثيل عام لإدارة التقارير
window.reportsManager = new ReportsManager();