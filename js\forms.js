/**
 * إدارة النماذج والتفاعلات للأرشفة العسكرية
 * يدير جميع النماذج والتحقق من البيانات
 */

class FormsManager {
    constructor() {
        this.currentStudents = [];
        this.attachments = [];
        this.initializeEventListeners();
    }

    /**
     * تهيئة مستمعي الأحداث
     */
    initializeEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            this.initializeDateInputs();
            this.initializeFormValidation();
        });
    }

    /**
     * تهيئة حقول التاريخ
     */
    initializeDateInputs() {
        const dateInputs = document.querySelectorAll('input[type="date"]');
        dateInputs.forEach(input => {
            if (!input.value) {
                input.value = new Date().toISOString().split('T')[0];
            }
        });
    }

    /**
     * تهيئة التحقق من النماذج
     */
    initializeFormValidation() {
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    }

    /**
     * إنشاء نموذج إضافة اليومية
     */
    createDailyForm() {
        return `
            <div class="daily-form-container">
                <form id="dailyForm" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="dailyDate" class="form-label">
                                    <i class="fas fa-calendar"></i> تاريخ اليومية
                                </label>
                                <input type="date" class="form-control" id="dailyDate" required>
                                <div class="invalid-feedback">
                                    يرجى تحديد تاريخ اليومية
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="supervisor" class="form-label">
                                    <i class="fas fa-user-tie"></i> اسم المشرف
                                </label>
                                <input type="text" class="form-control" id="supervisor" 
                                       placeholder="أدخل اسم المشرف" required>
                                <div class="invalid-feedback">
                                    يرجى إدخال اسم المشرف
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="students-section">
                        <div class="section-header">
                            <h4><i class="fas fa-users"></i> بيانات الطلاب</h4>
                            <button type="button" class="btn btn-primary" id="addStudentBtn">
                                <i class="fas fa-plus"></i> إضافة طالب
                            </button>
                        </div>
                        <div id="studentsContainer" class="students-container">
                            <!-- سيتم إضافة الطلاب هنا -->
                        </div>
                    </div>

                    <div class="attachments-section mt-4">
                        <div class="section-header">
                            <h4><i class="fas fa-paperclip"></i> المرفقات</h4>
                            <button type="button" class="btn btn-secondary" id="addAttachmentBtn">
                                <i class="fas fa-upload"></i> إضافة مرفق
                            </button>
                        </div>
                        <div id="attachmentsContainer" class="attachments-container">
                            <!-- سيتم إضافة المرفقات هنا -->
                        </div>
                        <input type="file" id="fileInput" multiple accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" style="display: none;">
                    </div>

                    <div class="form-actions mt-4">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-save"></i> حفظ اليومية
                        </button>
                        <button type="button" class="btn btn-secondary btn-lg" id="resetFormBtn">
                            <i class="fas fa-undo"></i> إعادة تعيين
                        </button>
                    </div>
                </form>
            </div>
        `;
    }

    /**
     * إنشاء نموذج إضافة طالب
     */
    createStudentForm(index = 0) {
        const violationOptions = dataManager.data.settings.violationTypes
            .map(type => `<option value="${type}">${type}</option>`)
            .join('');

        const absenceOptions = dataManager.data.settings.absenceTypes
            .map(type => `<option value="${type}">${type}</option>`)
            .join('');

        return `
            <div class="student-card" data-index="${index}">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user"></i> الطالب ${index + 1}
                            <button type="button" class="btn btn-sm btn-outline-danger float-start remove-student-btn">
                                <i class="fas fa-trash"></i>
                            </button>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">الاسم الكامل</label>
                                    <input type="text" class="form-control student-name" 
                                           placeholder="أدخل الاسم الكامل" required>
                                    <div class="search-suggestions" style="display: none;"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">الرقم العسكري</label>
                                    <input type="text" class="form-control student-military-number" 
                                           placeholder="أدخل الرقم العسكري" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label class="form-label">الرتبة</label>
                                    <select class="form-control student-rank" required>
                                        <option value="">اختر الرتبة</option>
                                        <option value="جندي">جندي</option>
                                        <option value="جندي أول">جندي أول</option>
                                        <option value="عريف">عريف</option>
                                        <option value="وكيل رقيب">وكيل رقيب</option>
                                        <option value="رقيب">رقيب</option>
                                        <option value="رقيب أول">رقيب أول</option>
                                        <option value="رئيس رقباء">رئيس رقباء</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label class="form-label">رمز الدورة</label>
                                    <input type="text" class="form-control student-course-code" 
                                           placeholder="رمز الدورة" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label class="form-label">رقم الدورة</label>
                                    <input type="text" class="form-control student-course-number" 
                                           placeholder="رقم الدورة" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="form-group mb-3">
                                    <label class="form-label">اسم الدورة</label>
                                    <input type="text" class="form-control student-course-name" 
                                           placeholder="سيتم ملؤه تلقائياً" readonly>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">المخالفات</label>
                                    <div class="violations-container">
                                        <select class="form-control violation-select">
                                            <option value="">اختر نوع المخالفة</option>
                                            ${violationOptions}
                                        </select>
                                        <button type="button" class="btn btn-sm btn-outline-primary mt-2 add-violation-btn">
                                            <i class="fas fa-plus"></i> إضافة مخالفة
                                        </button>
                                        <div class="violations-list mt-2"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group mb-3">
                                    <label class="form-label">التأخيرات (حصص)</label>
                                    <input type="number" class="form-control student-delays" 
                                           min="0" max="7" value="0">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group mb-3">
                                    <label class="form-label">نوع الغياب</label>
                                    <select class="form-control student-absence-type">
                                        <option value="">لا يوجد غياب</option>
                                        ${absenceOptions}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row absence-hours-row" style="display: none;">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">عدد ساعات الغياب</label>
                                    <input type="number" class="form-control student-absence-hours" 
                                           min="0" max="7" value="0">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * إنشاء نموذج إضافة دورة
     */
    createCourseForm() {
        return `
            <div class="course-form-container">
                <form id="courseForm" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="courseCode" class="form-label">
                                    <i class="fas fa-code"></i> رمز الدورة
                                </label>
                                <input type="text" class="form-control" id="courseCode" 
                                       placeholder="مثال: MIL" required>
                                <div class="invalid-feedback">
                                    يرجى إدخال رمز الدورة
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="courseNumber" class="form-label">
                                    <i class="fas fa-hashtag"></i> رقم الدورة
                                </label>
                                <input type="text" class="form-control" id="courseNumber" 
                                       placeholder="مثال: 001" required>
                                <div class="invalid-feedback">
                                    يرجى إدخال رقم الدورة
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="courseName" class="form-label">
                            <i class="fas fa-graduation-cap"></i> اسم الدورة
                        </label>
                        <input type="text" class="form-control" id="courseName" 
                               placeholder="أدخل اسم الدورة" required>
                        <div class="invalid-feedback">
                            يرجى إدخال اسم الدورة
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="startDate" class="form-label">
                                    <i class="fas fa-calendar-plus"></i> تاريخ بداية الدورة
                                </label>
                                <input type="date" class="form-control" id="startDate" required>
                                <div class="invalid-feedback">
                                    يرجى تحديد تاريخ بداية الدورة
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="endDate" class="form-label">
                                    <i class="fas fa-calendar-minus"></i> تاريخ نهاية الدورة
                                </label>
                                <input type="date" class="form-control" id="endDate" required>
                                <div class="invalid-feedback">
                                    يرجى تحديد تاريخ نهاية الدورة
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> حفظ الدورة
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>
        `;
    }

    /**
     * التحقق من صحة النموذج
     */
    validateForm(form) {
        let isValid = true;
        const inputs = form.querySelectorAll('input[required], select[required]');
        
        inputs.forEach(input => {
            if (!input.value.trim()) {
                isValid = false;
                input.classList.add('is-invalid');
            } else {
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
            }
        });

        return isValid;
    }

    /**
     * إعادة تعيين النموذج
     */
    resetForm(formId) {
        const form = document.getElementById(formId);
        if (form) {
            form.reset();
            form.classList.remove('was-validated');
            
            // إزالة جميع فئات التحقق
            const inputs = form.querySelectorAll('.is-valid, .is-invalid');
            inputs.forEach(input => {
                input.classList.remove('is-valid', 'is-invalid');
            });

            // إعادة تعيين البيانات الخاصة
            if (formId === 'dailyForm') {
                this.currentStudents = [];
                this.attachments = [];
                document.getElementById('studentsContainer').innerHTML = '';
                document.getElementById('attachmentsContainer').innerHTML = '';
            }
        }
    }

    /**
     * تطبيق البحث التلقائي للطلاب
     */
    setupStudentSearch(studentCard) {
        const nameInput = studentCard.querySelector('.student-name');
        const militaryNumberInput = studentCard.querySelector('.student-military-number');
        const suggestionsDiv = studentCard.querySelector('.search-suggestions');

        // البحث بالاسم
        nameInput.addEventListener('input', (e) => {
            const query = e.target.value;
            if (query.length >= 2) {
                const results = dataManager.searchStudent(query);
                this.showSearchSuggestions(suggestionsDiv, results, studentCard);
            } else {
                suggestionsDiv.style.display = 'none';
            }
        });

        // البحث بالرقم العسكري
        militaryNumberInput.addEventListener('input', (e) => {
            const militaryNumber = e.target.value;
            if (militaryNumber.length >= 3) {
                const student = dataManager.getStudentByMilitaryNumber(militaryNumber);
                if (student) {
                    this.fillStudentData(studentCard, student);
                }
            }
        });
    }

    /**
     * عرض اقتراحات البحث
     */
    showSearchSuggestions(container, results, studentCard) {
        if (results.length === 0) {
            container.style.display = 'none';
            return;
        }

        container.innerHTML = results.map(student => `
            <div class="suggestion-item" data-student='${JSON.stringify(student)}'>
                <strong>${student.name}</strong><br>
                <small>${student.militaryNumber} - ${student.rank}</small>
            </div>
        `).join('');

        container.style.display = 'block';

        // إضافة مستمعي الأحداث للاقتراحات
        container.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', () => {
                const student = JSON.parse(item.dataset.student);
                this.fillStudentData(studentCard, student);
                container.style.display = 'none';
            });
        });
    }

    /**
     * ملء بيانات الطالب
     */
    fillStudentData(studentCard, student) {
        studentCard.querySelector('.student-name').value = student.name;
        studentCard.querySelector('.student-military-number').value = student.militaryNumber;
        studentCard.querySelector('.student-rank').value = student.rank;
        studentCard.querySelector('.student-course-code').value = student.courseCode;
        studentCard.querySelector('.student-course-number').value = student.courseNumber;
        studentCard.querySelector('.student-course-name').value = student.courseName;
    }

    /**
     * إعداد البحث التلقائي للدورات
     */
    setupCourseSearch(studentCard) {
        const codeInput = studentCard.querySelector('.student-course-code');
        const numberInput = studentCard.querySelector('.student-course-number');
        const nameInput = studentCard.querySelector('.student-course-name');

        const searchCourse = () => {
            const code = codeInput.value.trim();
            const number = numberInput.value.trim();
            
            if (code && number) {
                const course = dataManager.searchCourse(code, number);
                if (course) {
                    nameInput.value = course.courseName;
                } else {
                    nameInput.value = '';
                }
            }
        };

        codeInput.addEventListener('input', searchCourse);
        numberInput.addEventListener('input', searchCourse);
    }
}

// إنشاء مثيل عام لإدارة النماذج
window.formsManager = new FormsManager();
