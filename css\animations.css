/* ===== التأثيرات الحركية للنظام العسكري ===== */

/* ===== الحركات الأساسية ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.3);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes zoomOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.3);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(10px);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(214, 158, 46, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(214, 158, 46, 0.8), 0 0 30px rgba(214, 158, 46, 0.6);
    }
}

/* ===== تطبيق الحركات على العناصر ===== */

/* حركة الهيدر */
.military-header {
    animation: slideInDown 0.8s ease;
}

/* حركة القائمة الجانبية */
.sidebar {
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.sidebar.active {
    animation: slideInRight 0.3s ease;
}

/* حركة عناصر القائمة */
.menu-item {
    opacity: 0;
    animation: fadeInRight 0.5s ease forwards;
}

.menu-item:nth-child(1) { animation-delay: 0.1s; }
.menu-item:nth-child(2) { animation-delay: 0.2s; }
.menu-item:nth-child(3) { animation-delay: 0.3s; }
.menu-item:nth-child(4) { animation-delay: 0.4s; }
.menu-item:nth-child(5) { animation-delay: 0.5s; }
.menu-item:nth-child(6) { animation-delay: 0.6s; }

/* حركة البانر الترحيبي */
.welcome-banner {
    animation: fadeInUp 1s ease;
}

.banner-content h2 {
    animation: fadeInDown 1.2s ease;
}

.banner-content p {
    animation: fadeInUp 1.4s ease;
}

/* حركة بطاقات الإحصائيات */
.stat-card {
    opacity: 0;
    animation: zoomIn 0.6s ease forwards;
}

.stat-card:nth-child(1) { animation-delay: 0.2s; }
.stat-card:nth-child(2) { animation-delay: 0.4s; }
.stat-card:nth-child(3) { animation-delay: 0.6s; }
.stat-card:nth-child(4) { animation-delay: 0.8s; }

/* حركة الأرقام في البطاقات */
.card-content h3 {
    animation: pulse 2s infinite;
}

/* حركة الأيقونات */
.card-icon i {
    animation: rotate 10s linear infinite;
}

/* حركة الأزرار */
.action-btn {
    opacity: 0;
    animation: bounceIn 0.8s ease forwards;
    position: relative;
    overflow: hidden;
}

.action-btn:nth-child(1) { animation-delay: 0.3s; }
.action-btn:nth-child(2) { animation-delay: 0.5s; }
.action-btn:nth-child(3) { animation-delay: 0.7s; }

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* تأثير الموجة على الأزرار */
.action-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.action-btn:active::before {
    width: 300px;
    height: 300px;
}

/* حركة رؤوس الصفحات */
.page-header {
    animation: slideInLeft 0.8s ease;
}

/* حركة المحتوى */
.page-content {
    animation: fadeInUp 0.5s ease;
}

/* حركة الإشعارات */
.notification {
    animation: slideInLeft 0.5s ease;
}

.notification.removing {
    animation: slideInRight 0.3s ease;
}

/* حركة النماذج */
.form-group {
    opacity: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.form-group:nth-child(3) { animation-delay: 0.3s; }
.form-group:nth-child(4) { animation-delay: 0.4s; }
.form-group:nth-child(5) { animation-delay: 0.5s; }

/* حركة الجداول */
.table-row {
    opacity: 0;
    animation: fadeInLeft 0.5s ease forwards;
}

.table-row:nth-child(odd) { animation-delay: 0.1s; }
.table-row:nth-child(even) { animation-delay: 0.2s; }

/* حركة النوافذ المنبثقة */
.modal {
    animation: zoomIn 0.3s ease;
}

.modal.closing {
    animation: zoomOut 0.3s ease;
}

/* حركة شاشة التحميل */
.loading-overlay {
    animation: fadeIn 0.3s ease;
}

.loading-spinner i {
    animation: rotate 1s linear infinite;
}

/* تأثيرات التفاعل */
.hover-glow:hover {
    animation: glow 1.5s ease-in-out infinite;
}

.hover-bounce:hover {
    animation: bounce 1s ease;
}

.hover-pulse:hover {
    animation: pulse 1s ease infinite;
}

.hover-shake:hover {
    animation: shake 0.5s ease;
}

/* حركات خاصة للعناصر العسكرية */
.military-badge {
    animation: pulse 2s ease-in-out infinite;
}

.rank-indicator {
    animation: glow 3s ease-in-out infinite;
}

.alert-indicator {
    animation: shake 0.5s ease infinite;
}

/* تأثيرات الانتقال بين الصفحات */
.page-transition-enter {
    animation: slideInRight 0.5s ease;
}

.page-transition-exit {
    animation: slideInLeft 0.5s ease;
}

/* حركات مخصصة للبيانات */
.data-loading {
    animation: pulse 1.5s ease-in-out infinite;
}

.data-success {
    animation: bounceIn 0.6s ease;
}

.data-error {
    animation: shake 0.5s ease;
}

/* تحسينات الأداء */
.animated-element {
    will-change: transform, opacity;
    backface-visibility: hidden;
    perspective: 1000px;
}

/* إيقاف الحركات للمستخدمين الذين يفضلون تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
