/**
 * الملف الرئيسي لنظام أرشفة اليوميات العسكرية
 * يدير التفاعلات الرئيسية والتنقل بين الصفحات
 */

class MilitaryArchiveApp {
    constructor() {
        this.currentPage = 'dashboard';
        this.sidebarOpen = false;
        this.init();
    }

    /**
     * تهيئة التطبيق
     */
    init() {
        this.setupEventListeners();
        this.loadDashboardStats();
        this.showPage('dashboard');
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // تبديل القائمة الجانبية
        document.getElementById('sidebarToggle').addEventListener('click', () => {
            this.toggleSidebar();
        });

        // التنقل في القائمة الجانبية
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const page = item.dataset.page;
                this.showPage(page);
                this.setActiveMenuItem(item);
                
                // إغلاق القائمة الجانبية في الشاشات الصغيرة
                if (window.innerWidth <= 768) {
                    this.closeSidebar();
                }
            });
        });

        // الإجراءات السريعة
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = btn.dataset.action;
                this.handleQuickAction(action);
            });
        });

        // إغلاق القائمة الجانبية عند النقر خارجها
        document.addEventListener('click', (e) => {
            const sidebar = document.getElementById('sidebar');
            const toggle = document.getElementById('sidebarToggle');
            
            if (this.sidebarOpen && !sidebar.contains(e.target) && !toggle.contains(e.target)) {
                this.closeSidebar();
            }
        });

        // التعامل مع تغيير حجم الشاشة
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768 && this.sidebarOpen) {
                document.getElementById('mainContent').classList.add('sidebar-open');
            } else {
                document.getElementById('mainContent').classList.remove('sidebar-open');
            }
        });
    }

    /**
     * تبديل حالة القائمة الجانبية
     */
    toggleSidebar() {
        if (this.sidebarOpen) {
            this.closeSidebar();
        } else {
            this.openSidebar();
        }
    }

    /**
     * فتح القائمة الجانبية
     */
    openSidebar() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        
        sidebar.classList.add('active');
        if (window.innerWidth > 768) {
            mainContent.classList.add('sidebar-open');
        }
        
        this.sidebarOpen = true;
    }

    /**
     * إغلاق القائمة الجانبية
     */
    closeSidebar() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        
        sidebar.classList.remove('active');
        mainContent.classList.remove('sidebar-open');
        
        this.sidebarOpen = false;
    }

    /**
     * عرض صفحة معينة
     */
    showPage(pageId) {
        // إخفاء جميع الصفحات
        document.querySelectorAll('.page-content').forEach(page => {
            page.classList.remove('active');
        });

        // عرض الصفحة المطلوبة
        const targetPage = document.getElementById(pageId);
        if (targetPage) {
            targetPage.classList.add('active');
            this.currentPage = pageId;
            
            // تحميل محتوى الصفحة
            this.loadPageContent(pageId);
        }
    }

    /**
     * تعيين العنصر النشط في القائمة
     */
    setActiveMenuItem(activeItem) {
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });
        activeItem.classList.add('active');
    }

    /**
     * تحميل محتوى الصفحة
     */
    loadPageContent(pageId) {
        switch (pageId) {
            case 'dashboard':
                this.loadDashboardStats();
                break;
            case 'add-daily':
                this.loadDailyForm();
                break;
            case 'daily-records':
                this.loadDailyRecords();
                break;
            case 'courses':
                this.loadCoursesPage();
                break;
            case 'users':
                this.loadUsersPage();
                break;
            case 'settings':
                this.loadSettingsPage();
                break;
        }
    }

    /**
     * تحميل إحصائيات لوحة التحكم
     */
    loadDashboardStats() {
        document.getElementById('totalViolations').textContent = dataManager.getTotalViolations();
        document.getElementById('totalDelays').textContent = dataManager.getTotalDelays();
        document.getElementById('totalAbsences').textContent = dataManager.getTotalAbsences();
        document.getElementById('totalStudents').textContent = dataManager.getTotalStudents();
    }

    /**
     * تحميل نموذج اليومية
     */
    loadDailyForm() {
        const container = document.querySelector('#add-daily .container-fluid');
        if (container.children.length <= 1) { // فقط العنوان موجود
            const formHTML = formsManager.createDailyForm();
            container.insertAdjacentHTML('beforeend', formHTML);
            this.setupDailyFormEvents();
        }
    }

    /**
     * إعداد أحداث نموذج اليومية
     */
    setupDailyFormEvents() {
        // إضافة طالب جديد
        document.getElementById('addStudentBtn').addEventListener('click', () => {
            this.addStudentToForm();
        });

        // إضافة مرفق
        document.getElementById('addAttachmentBtn').addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });

        // المسح الضوئي
        document.getElementById('scanDocumentBtn').addEventListener('click', () => {
            scanner.openScanner();
        });

        // معالجة رفع الملفات
        document.getElementById('fileInput').addEventListener('change', (e) => {
            this.handleFileUpload(e.files);
        });

        // حفظ النموذج
        document.getElementById('dailyForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveDailyRecord();
        });

        // إعادة تعيين النموذج
        document.getElementById('resetFormBtn').addEventListener('click', () => {
            this.resetDailyForm();
        });

        // إضافة طالب افتراضي
        this.addStudentToForm();
    }

    /**
     * إضافة طالب جديد للنموذج
     */
    addStudentToForm() {
        const container = document.getElementById('studentsContainer');
        const index = container.children.length;
        const studentHTML = formsManager.createStudentForm(index);
        
        container.insertAdjacentHTML('beforeend', studentHTML);
        
        const studentCard = container.lastElementChild;
        this.setupStudentCardEvents(studentCard);
    }

    /**
     * إعداد أحداث بطاقة الطالب
     */
    setupStudentCardEvents(studentCard) {
        // إزالة الطالب
        studentCard.querySelector('.remove-student-btn').addEventListener('click', () => {
            if (document.querySelectorAll('.student-card').length > 1) {
                studentCard.remove();
                notificationManager.showInfo('تم حذف الطالب');
            } else {
                notificationManager.showWarning('يجب وجود طالب واحد على الأقل');
            }
        });

        // البحث التلقائي للطلاب
        formsManager.setupStudentSearch(studentCard);
        
        // البحث التلقائي للدورات
        formsManager.setupCourseSearch(studentCard);

        // إضافة مخالفة
        studentCard.querySelector('.add-violation-btn').addEventListener('click', () => {
            this.addViolation(studentCard);
        });

        // عرض/إخفاء ساعات الغياب
        studentCard.querySelector('.student-absence-type').addEventListener('change', (e) => {
            const absenceHoursRow = studentCard.querySelector('.absence-hours-row');
            if (e.target.value && e.target.value !== '') {
                absenceHoursRow.style.display = 'block';
            } else {
                absenceHoursRow.style.display = 'none';
            }
        });
    }

    /**
     * إضافة مخالفة للطالب
     */
    addViolation(studentCard) {
        const select = studentCard.querySelector('.violation-select');
        const violationsList = studentCard.querySelector('.violations-list');
        
        if (select.value) {
            const violationItem = document.createElement('div');
            violationItem.className = 'violation-item badge bg-danger me-2 mb-2';
            violationItem.innerHTML = `
                ${select.value}
                <button type="button" class="btn-close btn-close-white ms-2" onclick="this.parentElement.remove()"></button>
            `;
            
            violationsList.appendChild(violationItem);
            select.value = '';
            
            notificationManager.showMilitary('تم إضافة مخالفة جديدة', 'إضافة مخالفة');
        }
    }

    /**
     * معالجة رفع الملفات
     */
    handleFileUpload(files) {
        const container = document.getElementById('attachmentsContainer');
        
        Array.from(files).forEach(file => {
            if (file.size > 10 * 1024 * 1024) { // 10MB
                notificationManager.showError('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت');
                return;
            }

            const attachmentItem = document.createElement('div');
            attachmentItem.className = 'attachment-item';
            attachmentItem.innerHTML = `
                <div class="card mb-2">
                    <div class="card-body p-2">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file me-2"></i>
                            <span class="flex-grow-1">${file.name}</span>
                            <small class="text-muted me-2">${this.formatFileSize(file.size)}</small>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="this.closest('.attachment-item').remove()">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            container.appendChild(attachmentItem);
        });

        notificationManager.showSuccess('تم إضافة المرفقات بنجاح');
    }

    /**
     * تنسيق حجم الملف
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * حفظ سجل اليومية
     */
    saveDailyRecord() {
        this.showLoading(true);
        
        try {
            const formData = this.collectDailyFormData();
            const record = dataManager.addDailyRecord(formData);
            
            notificationManager.showSuccess('تم حفظ اليومية بنجاح');
            this.resetDailyForm();
            this.loadDashboardStats();
            
        } catch (error) {
            notificationManager.showError('حدث خطأ أثناء حفظ اليومية: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * جمع بيانات نموذج اليومية
     */
    collectDailyFormData() {
        const date = document.getElementById('dailyDate').value;
        const supervisor = document.getElementById('supervisor').value;
        const students = [];

        document.querySelectorAll('.student-card').forEach(card => {
            const violations = Array.from(card.querySelectorAll('.violation-item'))
                .map(item => item.textContent.trim().replace('×', ''));

            const student = {
                name: card.querySelector('.student-name').value,
                militaryNumber: card.querySelector('.student-military-number').value,
                rank: card.querySelector('.student-rank').value,
                courseCode: card.querySelector('.student-course-code').value,
                courseNumber: card.querySelector('.student-course-number').value,
                courseName: card.querySelector('.student-course-name').value,
                violations: violations,
                delays: parseInt(card.querySelector('.student-delays').value) || 0,
                absenceType: card.querySelector('.student-absence-type').value,
                absenceHours: parseInt(card.querySelector('.student-absence-hours').value) || 0
            };

            students.push(student);
        });

        return {
            date: date,
            supervisor: supervisor,
            students: students,
            attachments: [] // سيتم تطوير نظام المرفقات لاحقاً
        };
    }

    /**
     * إعادة تعيين نموذج اليومية
     */
    resetDailyForm() {
        formsManager.resetForm('dailyForm');
        document.getElementById('studentsContainer').innerHTML = '';
        document.getElementById('attachmentsContainer').innerHTML = '';
        this.addStudentToForm();
        
        // إعادة تعيين التاريخ للتاريخ الحالي
        document.getElementById('dailyDate').value = new Date().toISOString().split('T')[0];
    }

    /**
     * معالجة الإجراءات السريعة
     */
    handleQuickAction(action) {
        switch (action) {
            case 'add-daily':
                this.showPage('add-daily');
                this.setActiveMenuItem(document.querySelector('[data-page="add-daily"]'));
                break;
            case 'view-records':
                this.showPage('daily-records');
                this.setActiveMenuItem(document.querySelector('[data-page="daily-records"]'));
                break;
            case 'add-course':
                this.showAddCourseModal();
                break;
        }
    }

    /**
     * عرض نافذة إضافة دورة
     */
    showAddCourseModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-graduation-cap"></i> إضافة دورة جديدة
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${formsManager.createCourseForm()}
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);
        
        // معالجة حفظ الدورة
        modal.querySelector('#courseForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveCourse(modal, bootstrapModal);
        });

        bootstrapModal.show();
    }

    /**
     * حفظ الدورة
     */
    saveCourse(modal, bootstrapModal) {
        try {
            const formData = {
                courseCode: modal.querySelector('#courseCode').value,
                courseNumber: modal.querySelector('#courseNumber').value,
                courseName: modal.querySelector('#courseName').value,
                startDate: modal.querySelector('#startDate').value,
                endDate: modal.querySelector('#endDate').value
            };

            dataManager.addCourse(formData);
            notificationManager.showSuccess('تم إضافة الدورة بنجاح');
            bootstrapModal.hide();
            
        } catch (error) {
            notificationManager.showError('حدث خطأ: ' + error.message);
        }
    }

    /**
     * عرض/إخفاء شاشة التحميل
     */
    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        overlay.style.display = show ? 'flex' : 'none';
    }

    // ===== صفحة السجلات =====
    loadDailyRecords() {
        const container = document.querySelector('#daily-records .container-fluid');
        if (container.children.length <= 1) {
            const recordsHTML = this.createDailyRecordsPage();
            container.insertAdjacentHTML('beforeend', recordsHTML);
            this.setupRecordsPageEvents();
            this.loadRecordsList();
        }
    }

    createDailyRecordsPage() {
        return `
            <div class="records-page">
                <div class="filters-section mb-4">
                    <div class="info-card">
                        <h5><i class="fas fa-filter"></i> فلترة السجلات</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">التاريخ</label>
                                    <input type="date" class="form-control" id="filterDate">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">المشرف</label>
                                    <input type="text" class="form-control" id="filterSupervisor" placeholder="اسم المشرف">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">اسم الطالب</label>
                                    <input type="text" class="form-control" id="filterStudent" placeholder="اسم الطالب">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-primary" id="applyFilters">
                                            <i class="fas fa-search"></i> بحث
                                        </button>
                                        <button class="btn btn-secondary" id="clearFilters">
                                            <i class="fas fa-times"></i> مسح
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="records-list">
                    <div class="table-container">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5><i class="fas fa-list"></i> قائمة اليوميات</h5>
                            <span class="badge bg-primary" id="recordsCount">0 سجل</span>
                        </div>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>المشرف</th>
                                        <th>عدد الطلاب</th>
                                        <th>المخالفات</th>
                                        <th>التأخيرات</th>
                                        <th>الغيابات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="recordsTableBody">
                                    <!-- سيتم إضافة السجلات هنا -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupRecordsPageEvents() {
        // تطبيق الفلاتر
        document.getElementById('applyFilters').addEventListener('click', () => {
            this.applyRecordsFilters();
        });

        // مسح الفلاتر
        document.getElementById('clearFilters').addEventListener('click', () => {
            this.clearRecordsFilters();
        });

        // البحث التلقائي
        ['filterDate', 'filterSupervisor', 'filterStudent'].forEach(id => {
            document.getElementById(id).addEventListener('input', () => {
                this.applyRecordsFilters();
            });
        });
    }

    loadRecordsList() {
        const records = dataManager.getAllDailyRecords();
        this.displayRecords(records);
    }

    applyRecordsFilters() {
        const filters = {
            date: document.getElementById('filterDate').value,
            supervisor: document.getElementById('filterSupervisor').value,
            studentName: document.getElementById('filterStudent').value
        };

        const filteredRecords = dataManager.searchDailyRecords(filters);
        this.displayRecords(filteredRecords);
    }

    clearRecordsFilters() {
        document.getElementById('filterDate').value = '';
        document.getElementById('filterSupervisor').value = '';
        document.getElementById('filterStudent').value = '';
        this.loadRecordsList();
    }

    displayRecords(records) {
        const tbody = document.getElementById('recordsTableBody');
        const countBadge = document.getElementById('recordsCount');

        countBadge.textContent = `${records.length} سجل`;

        if (records.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-2x mb-2"></i><br>
                        لا توجد سجلات مطابقة للبحث
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = records.map(record => {
            const totalViolations = record.students.reduce((sum, student) =>
                sum + (student.violations ? student.violations.length : 0), 0);
            const totalDelays = record.students.reduce((sum, student) =>
                sum + (student.delays || 0), 0);
            const totalAbsences = record.students.reduce((sum, student) =>
                sum + (student.absenceHours || 0), 0);

            return `
                <tr class="table-row">
                    <td>${dataManager.formatDate(record.date)}</td>
                    <td>${record.supervisor}</td>
                    <td>${record.students.length}</td>
                    <td>
                        <span class="badge bg-danger">${totalViolations}</span>
                    </td>
                    <td>
                        <span class="badge bg-warning">${totalDelays}</span>
                    </td>
                    <td>
                        <span class="badge bg-info">${totalAbsences}</span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="app.viewRecordDetails('${record.id}')">
                            <i class="fas fa-eye"></i> عرض
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    viewRecordDetails(recordId) {
        const record = dataManager.data.dailyRecords.find(r => r.id === recordId);
        if (!record) return;

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-file-alt"></i> تفاصيل اليومية - ${dataManager.formatDate(record.date)}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>التاريخ:</strong> ${dataManager.formatDate(record.date)}
                            </div>
                            <div class="col-md-6">
                                <strong>المشرف:</strong> ${record.supervisor}
                            </div>
                        </div>

                        <h6><i class="fas fa-users"></i> قائمة الطلاب</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>الرقم العسكري</th>
                                        <th>الرتبة</th>
                                        <th>الدورة</th>
                                        <th>المخالفات</th>
                                        <th>التأخيرات</th>
                                        <th>الغياب</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${record.students.map(student => `
                                        <tr>
                                            <td>${student.name}</td>
                                            <td>${student.militaryNumber}</td>
                                            <td>${student.rank}</td>
                                            <td>${student.courseName}</td>
                                            <td>
                                                ${student.violations && student.violations.length > 0
                                                    ? student.violations.map(v => `<span class="badge bg-danger me-1">${v}</span>`).join('')
                                                    : '<span class="text-muted">لا يوجد</span>'
                                                }
                                            </td>
                                            <td>
                                                ${student.delays > 0
                                                    ? `<span class="badge bg-warning">${student.delays} حصة</span>`
                                                    : '<span class="text-muted">لا يوجد</span>'
                                                }
                                            </td>
                                            <td>
                                                ${student.absenceType
                                                    ? `<span class="badge bg-info">${student.absenceType} (${student.absenceHours} ساعة)</span>`
                                                    : '<span class="text-muted">لا يوجد</span>'
                                                }
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);

        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });

        bootstrapModal.show();
    }

    // ===== صفحة الدورات =====
    loadCoursesPage() {
        const container = document.querySelector('#courses .container-fluid');
        if (container.children.length <= 1) {
            const coursesHTML = this.createCoursesPage();
            container.insertAdjacentHTML('beforeend', coursesHTML);
            this.setupCoursesPageEvents();
            this.loadCoursesList();
        }
    }

    createCoursesPage() {
        return `
            <div class="courses-page">
                <div class="row">
                    <div class="col-md-8">
                        <div class="table-container">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5><i class="fas fa-graduation-cap"></i> قائمة الدورات</h5>
                                <button class="btn btn-primary" id="addCourseBtn">
                                    <i class="fas fa-plus"></i> إضافة دورة جديدة
                                </button>
                            </div>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>رمز الدورة</th>
                                            <th>رقم الدورة</th>
                                            <th>اسم الدورة</th>
                                            <th>تاريخ البداية</th>
                                            <th>تاريخ النهاية</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="coursesTableBody">
                                        <!-- سيتم إضافة الدورات هنا -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-card">
                            <h5><i class="fas fa-chart-pie"></i> إحصائيات الدورات</h5>
                            <div class="stats-list">
                                <div class="stat-item">
                                    <span class="stat-label">إجمالي الدورات:</span>
                                    <span class="stat-value" id="totalCoursesCount">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">الدورات النشطة:</span>
                                    <span class="stat-value" id="activeCoursesCount">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">الدورات المكتملة:</span>
                                    <span class="stat-value" id="completedCoursesCount">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupCoursesPageEvents() {
        document.getElementById('addCourseBtn').addEventListener('click', () => {
            this.showAddCourseModal();
        });
    }

    loadCoursesList() {
        const courses = dataManager.getAllCourses();
        this.displayCourses(courses);
        this.updateCoursesStats(courses);
    }

    displayCourses(courses) {
        const tbody = document.getElementById('coursesTableBody');

        if (courses.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-muted py-4">
                        <i class="fas fa-graduation-cap fa-2x mb-2"></i><br>
                        لا توجد دورات مسجلة
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = courses.map(course => `
            <tr class="table-row">
                <td>${course.courseCode}</td>
                <td>${course.courseNumber}</td>
                <td>${course.courseName}</td>
                <td>${dataManager.formatDate(course.startDate)}</td>
                <td>${dataManager.formatDate(course.endDate)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="app.editCourse('${course.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="app.deleteCourse('${course.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    updateCoursesStats(courses) {
        const today = new Date();
        const activeCourses = courses.filter(course =>
            new Date(course.startDate) <= today && new Date(course.endDate) >= today
        );
        const completedCourses = courses.filter(course =>
            new Date(course.endDate) < today
        );

        document.getElementById('totalCoursesCount').textContent = courses.length;
        document.getElementById('activeCoursesCount').textContent = activeCourses.length;
        document.getElementById('completedCoursesCount').textContent = completedCourses.length;
    }

    editCourse(courseId) {
        // سيتم تطوير وظيفة التعديل
        notificationManager.showInfo('وظيفة التعديل قيد التطوير');
    }

    deleteCourse(courseId) {
        notificationManager.showConfirm(
            'هل أنت متأكد من حذف هذه الدورة؟',
            'تأكيد الحذف',
            () => {
                // سيتم تطوير وظيفة الحذف
                notificationManager.showSuccess('تم حذف الدورة بنجاح');
                this.loadCoursesList();
            }
        );
    }

    // ===== صفحة إدارة المستخدمين =====
    loadUsersPage() {
        const container = document.querySelector('#users .container-fluid');
        if (container.children.length <= 1) {
            const usersHTML = this.createUsersPage();
            container.insertAdjacentHTML('beforeend', usersHTML);
            this.setupUsersPageEvents();
            this.loadUsersList();
        }
    }

    createUsersPage() {
        return `
            <div class="users-page">
                <div class="row">
                    <div class="col-md-8">
                        <div class="table-container">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5><i class="fas fa-users"></i> قائمة المستخدمين</h5>
                                <button class="btn btn-primary" id="addUserBtn">
                                    <i class="fas fa-user-plus"></i> إضافة مستخدم جديد
                                </button>
                            </div>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>الاسم</th>
                                            <th>اسم المستخدم</th>
                                            <th>الدور</th>
                                            <th>الصلاحيات</th>
                                            <th>تاريخ الإنشاء</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="usersTableBody">
                                        <!-- سيتم إضافة المستخدمين هنا -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-card">
                            <h5><i class="fas fa-chart-pie"></i> إحصائيات المستخدمين</h5>
                            <div class="stats-list">
                                <div class="stat-item">
                                    <span class="stat-label">إجمالي المستخدمين:</span>
                                    <span class="stat-value" id="totalUsersCount">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">المشرفين:</span>
                                    <span class="stat-value" id="adminsCount">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">المراقبين:</span>
                                    <span class="stat-value" id="supervisorsCount">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">الضباط:</span>
                                    <span class="stat-value" id="officersCount">0</span>
                                </div>
                            </div>
                        </div>

                        <div class="info-card mt-3">
                            <h5><i class="fas fa-shield-alt"></i> الأدوار والصلاحيات</h5>
                            <div class="roles-info">
                                <div class="role-item">
                                    <strong>المشرف العام (admin):</strong>
                                    <small class="d-block text-muted">جميع الصلاحيات</small>
                                </div>
                                <div class="role-item">
                                    <strong>المراقب (supervisor):</strong>
                                    <small class="d-block text-muted">إضافة اليوميات وعرض السجلات</small>
                                </div>
                                <div class="role-item">
                                    <strong>الضابط (officer):</strong>
                                    <small class="d-block text-muted">عرض السجلات فقط</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupUsersPageEvents() {
        document.getElementById('addUserBtn').addEventListener('click', () => {
            this.showAddUserModal();
        });
    }

    loadUsersList() {
        const users = dataManager.data.users || [];
        this.displayUsers(users);
        this.updateUsersStats(users);
    }

    displayUsers(users) {
        const tbody = document.getElementById('usersTableBody');

        if (users.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-muted py-4">
                        <i class="fas fa-users fa-2x mb-2"></i><br>
                        لا توجد مستخدمين مسجلين
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = users.map(user => {
            const roleNames = {
                'admin': 'مشرف عام',
                'supervisor': 'مراقب',
                'officer': 'ضابط'
            };

            const permissionNames = {
                'all': 'جميع الصلاحيات',
                'add_daily': 'إضافة اليوميات',
                'view_records': 'عرض السجلات',
                'manage_users': 'إدارة المستخدمين',
                'manage_courses': 'إدارة الدورات'
            };

            const permissions = Array.isArray(user.permissions)
                ? user.permissions.map(p => permissionNames[p] || p).join(', ')
                : 'غير محدد';

            return `
                <tr class="table-row">
                    <td>${user.name}</td>
                    <td>${user.username}</td>
                    <td>
                        <span class="badge bg-${this.getRoleBadgeColor(user.role)}">
                            ${roleNames[user.role] || user.role}
                        </span>
                    </td>
                    <td>
                        <small>${permissions}</small>
                    </td>
                    <td>${user.createdAt ? dataManager.formatDate(user.createdAt) : 'غير محدد'}</td>
                    <td>
                        ${user.id !== 1 ? `
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="app.editUser(${user.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="app.deleteUser(${user.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : `
                            <span class="badge bg-warning">محمي</span>
                        `}
                    </td>
                </tr>
            `;
        }).join('');
    }

    getRoleBadgeColor(role) {
        const colors = {
            'admin': 'danger',
            'supervisor': 'primary',
            'officer': 'info'
        };
        return colors[role] || 'secondary';
    }

    updateUsersStats(users) {
        const admins = users.filter(u => u.role === 'admin').length;
        const supervisors = users.filter(u => u.role === 'supervisor').length;
        const officers = users.filter(u => u.role === 'officer').length;

        document.getElementById('totalUsersCount').textContent = users.length;
        document.getElementById('adminsCount').textContent = admins;
        document.getElementById('supervisorsCount').textContent = supervisors;
        document.getElementById('officersCount').textContent = officers;
    }

    showAddUserModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-user-plus"></i> إضافة مستخدم جديد
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="userForm" class="needs-validation" novalidate>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="userName" class="form-label">
                                            <i class="fas fa-user"></i> الاسم الكامل
                                        </label>
                                        <input type="text" class="form-control" id="userName"
                                               placeholder="أدخل الاسم الكامل" required>
                                        <div class="invalid-feedback">
                                            يرجى إدخال الاسم الكامل
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="userUsername" class="form-label">
                                            <i class="fas fa-at"></i> اسم المستخدم
                                        </label>
                                        <input type="text" class="form-control" id="userUsername"
                                               placeholder="أدخل اسم المستخدم" required>
                                        <div class="invalid-feedback">
                                            يرجى إدخال اسم المستخدم
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="userRole" class="form-label">
                                            <i class="fas fa-user-tag"></i> الدور
                                        </label>
                                        <select class="form-control" id="userRole" required>
                                            <option value="">اختر الدور</option>
                                            <option value="supervisor">مراقب</option>
                                            <option value="officer">ضابط</option>
                                            <option value="admin">مشرف عام</option>
                                        </select>
                                        <div class="invalid-feedback">
                                            يرجى اختيار الدور
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="userPassword" class="form-label">
                                            <i class="fas fa-lock"></i> كلمة المرور
                                        </label>
                                        <input type="password" class="form-control" id="userPassword"
                                               placeholder="أدخل كلمة المرور" required>
                                        <div class="invalid-feedback">
                                            يرجى إدخال كلمة المرور
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group mb-3">
                                <label class="form-label">
                                    <i class="fas fa-shield-alt"></i> الصلاحيات
                                </label>
                                <div id="permissionsContainer">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="add_daily" id="perm_add_daily">
                                        <label class="form-check-label" for="perm_add_daily">
                                            إضافة اليوميات
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="view_records" id="perm_view_records">
                                        <label class="form-check-label" for="perm_view_records">
                                            عرض السجلات
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="manage_courses" id="perm_manage_courses">
                                        <label class="form-check-label" for="perm_manage_courses">
                                            إدارة الدورات
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="manage_users" id="perm_manage_users">
                                        <label class="form-check-label" for="perm_manage_users">
                                            إدارة المستخدمين
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                        <button type="button" class="btn btn-primary" id="saveUserBtn">
                            <i class="fas fa-save"></i> حفظ المستخدم
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);

        // تحديث الصلاحيات عند تغيير الدور
        modal.querySelector('#userRole').addEventListener('change', (e) => {
            this.updatePermissionsByRole(e.target.value, modal);
        });

        // معالجة حفظ المستخدم
        modal.querySelector('#saveUserBtn').addEventListener('click', () => {
            this.saveUser(modal, bootstrapModal);
        });

        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });

        bootstrapModal.show();
    }

    updatePermissionsByRole(role, modal) {
        const checkboxes = modal.querySelectorAll('#permissionsContainer input[type="checkbox"]');

        // إلغاء تحديد جميع الصلاحيات
        checkboxes.forEach(cb => cb.checked = false);

        // تحديد الصلاحيات حسب الدور
        switch (role) {
            case 'admin':
                checkboxes.forEach(cb => cb.checked = true);
                break;
            case 'supervisor':
                modal.querySelector('#perm_add_daily').checked = true;
                modal.querySelector('#perm_view_records').checked = true;
                break;
            case 'officer':
                modal.querySelector('#perm_view_records').checked = true;
                break;
        }
    }

    saveUser(modal, bootstrapModal) {
        const form = modal.querySelector('#userForm');
        if (!formsManager.validateForm(form)) {
            return;
        }

        try {
            const permissions = Array.from(modal.querySelectorAll('#permissionsContainer input:checked'))
                .map(cb => cb.value);

            const userData = {
                name: modal.querySelector('#userName').value,
                username: modal.querySelector('#userUsername').value,
                role: modal.querySelector('#userRole').value,
                password: modal.querySelector('#userPassword').value, // في التطبيق الحقيقي يجب تشفيرها
                permissions: permissions,
                createdAt: new Date().toISOString()
            };

            // التحقق من عدم وجود اسم المستخدم مسبقاً
            if (dataManager.data.users.find(u => u.username === userData.username)) {
                notificationManager.showError('اسم المستخدم موجود مسبقاً');
                return;
            }

            // إضافة المستخدم
            const newUser = {
                id: dataManager.generateId(),
                ...userData
            };

            dataManager.data.users.push(newUser);
            dataManager.saveData();

            notificationManager.showSuccess('تم إضافة المستخدم بنجاح');
            bootstrapModal.hide();
            this.loadUsersList();

        } catch (error) {
            notificationManager.showError('حدث خطأ: ' + error.message);
        }
    }

    editUser(userId) {
        const user = dataManager.data.users.find(u => u.id === userId);
        if (!user) return;

        // سيتم تطوير نافذة التعديل
        notificationManager.showInfo('وظيفة التعديل قيد التطوير');
    }

    deleteUser(userId) {
        const user = dataManager.data.users.find(u => u.id === userId);
        if (!user) return;

        notificationManager.showConfirm(
            `هل أنت متأكد من حذف المستخدم "${user.name}"؟`,
            'تأكيد الحذف',
            () => {
                dataManager.data.users = dataManager.data.users.filter(u => u.id !== userId);
                dataManager.saveData();
                notificationManager.showSuccess('تم حذف المستخدم بنجاح');
                this.loadUsersList();
            }
        );
    }

    // ===== صفحة الإعدادات =====
    loadSettingsPage() {
        const container = document.querySelector('#settings .container-fluid');
        if (container.children.length <= 1) {
            const settingsHTML = this.createSettingsPage();
            container.insertAdjacentHTML('beforeend', settingsHTML);
            this.setupSettingsPageEvents();
            this.loadCurrentSettings();
        }
    }

    createSettingsPage() {
        return `
            <div class="settings-page">
                <div class="row">
                    <div class="col-md-8">
                        <!-- إعدادات عامة -->
                        <div class="info-card mb-4">
                            <h5><i class="fas fa-cog"></i> الإعدادات العامة</h5>
                            <form id="generalSettingsForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="maxDelayHours" class="form-label">
                                                الحد الأقصى لساعات التأخير
                                            </label>
                                            <input type="number" class="form-control" id="maxDelayHours"
                                                   min="1" max="24" value="7">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="maxAbsenceHours" class="form-label">
                                                الحد الأقصى لساعات الغياب
                                            </label>
                                            <input type="number" class="form-control" id="maxAbsenceHours"
                                                   min="1" max="24" value="7">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group mb-3">
                                    <label for="systemTitle" class="form-label">
                                        عنوان النظام
                                    </label>
                                    <input type="text" class="form-control" id="systemTitle"
                                           value="نظام أرشفة اليوميات العسكرية">
                                </div>
                                <div class="form-group mb-3">
                                    <label for="organizationName" class="form-label">
                                        اسم المؤسسة
                                    </label>
                                    <input type="text" class="form-control" id="organizationName"
                                           placeholder="أدخل اسم المؤسسة">
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ الإعدادات العامة
                                </button>
                            </form>
                        </div>

                        <!-- إدارة أنواع المخالفات -->
                        <div class="info-card mb-4">
                            <h5><i class="fas fa-exclamation-triangle"></i> إدارة أنواع المخالفات</h5>
                            <div class="mb-3">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="newViolationType"
                                           placeholder="أدخل نوع مخالفة جديد">
                                    <button class="btn btn-outline-primary" id="addViolationBtn">
                                        <i class="fas fa-plus"></i> إضافة
                                    </button>
                                </div>
                            </div>
                            <div id="violationsList" class="violations-list">
                                <!-- سيتم إضافة المخالفات هنا -->
                            </div>
                        </div>

                        <!-- إدارة أنواع الغياب -->
                        <div class="info-card mb-4">
                            <h5><i class="fas fa-user-times"></i> إدارة أنواع الغياب</h5>
                            <div class="mb-3">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="newAbsenceType"
                                           placeholder="أدخل نوع غياب جديد">
                                    <button class="btn btn-outline-primary" id="addAbsenceBtn">
                                        <i class="fas fa-plus"></i> إضافة
                                    </button>
                                </div>
                            </div>
                            <div id="absencesList" class="absences-list">
                                <!-- سيتم إضافة أنواع الغياب هنا -->
                            </div>
                        </div>

                        <!-- إدارة الرتب العسكرية -->
                        <div class="info-card mb-4">
                            <h5><i class="fas fa-star"></i> إدارة الرتب العسكرية</h5>
                            <div class="mb-3">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="newRank"
                                           placeholder="أدخل رتبة عسكرية جديدة">
                                    <button class="btn btn-outline-primary" id="addRankBtn">
                                        <i class="fas fa-plus"></i> إضافة
                                    </button>
                                </div>
                            </div>
                            <div id="ranksList" class="ranks-list">
                                <!-- سيتم إضافة الرتب هنا -->
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <!-- إعدادات النظام -->
                        <div class="info-card mb-4">
                            <h5><i class="fas fa-palette"></i> إعدادات المظهر</h5>
                            <div class="form-group mb-3">
                                <label class="form-label">نمط الألوان</label>
                                <select class="form-control" id="colorTheme">
                                    <option value="default">الافتراضي (عسكري)</option>
                                    <option value="dark">الوضع الليلي</option>
                                    <option value="blue">الأزرق الكلاسيكي</option>
                                </select>
                            </div>
                            <div class="form-group mb-3">
                                <label class="form-label">حجم الخط</label>
                                <select class="form-control" id="fontSize">
                                    <option value="small">صغير</option>
                                    <option value="medium" selected>متوسط</option>
                                    <option value="large">كبير</option>
                                </select>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="enableAnimations" checked>
                                <label class="form-check-label" for="enableAnimations">
                                    تفعيل التأثيرات الحركية
                                </label>
                            </div>
                            <button class="btn btn-secondary w-100" id="saveAppearanceBtn">
                                <i class="fas fa-save"></i> حفظ إعدادات المظهر
                            </button>
                        </div>

                        <!-- إعدادات النسخ الاحتياطي -->
                        <div class="info-card mb-4">
                            <h5><i class="fas fa-database"></i> النسخ الاحتياطي</h5>
                            <div class="d-grid gap-2">
                                <button class="btn btn-success" id="exportDataBtn">
                                    <i class="fas fa-download"></i> تصدير البيانات
                                </button>
                                <button class="btn btn-warning" id="importDataBtn">
                                    <i class="fas fa-upload"></i> استيراد البيانات
                                </button>
                                <button class="btn btn-info" id="downloadBackupBtn">
                                    <i class="fas fa-database"></i> إدارة النسخ الاحتياطية
                                </button>
                            </div>
                            <input type="file" id="importFileInput" accept=".json" style="display: none;">
                        </div>

                        <!-- إعدادات متقدمة -->
                        <div class="info-card">
                            <h5><i class="fas fa-tools"></i> إعدادات متقدمة</h5>
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-info" id="clearCacheBtn">
                                    <i class="fas fa-broom"></i> مسح ذاكرة التخزين المؤقت
                                </button>
                                <button class="btn btn-outline-warning" id="resetSettingsBtn">
                                    <i class="fas fa-undo"></i> إعادة تعيين الإعدادات
                                </button>
                                <button class="btn btn-outline-danger" id="clearAllDataBtn">
                                    <i class="fas fa-trash-alt"></i> مسح جميع البيانات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupSettingsPageEvents() {
        // حفظ الإعدادات العامة
        document.getElementById('generalSettingsForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveGeneralSettings();
        });

        // إضافة أنواع المخالفات
        document.getElementById('addViolationBtn').addEventListener('click', () => {
            this.addViolationType();
        });

        // إضافة أنواع الغياب
        document.getElementById('addAbsenceBtn').addEventListener('click', () => {
            this.addAbsenceType();
        });

        // إضافة الرتب
        document.getElementById('addRankBtn').addEventListener('click', () => {
            this.addRank();
        });

        // إعدادات المظهر
        document.getElementById('saveAppearanceBtn').addEventListener('click', () => {
            this.saveAppearanceSettings();
        });

        // النسخ الاحتياطي
        document.getElementById('exportDataBtn').addEventListener('click', () => {
            backupManager.exportBackup();
        });

        document.getElementById('importDataBtn').addEventListener('click', () => {
            document.getElementById('importFileInput').click();
        });

        document.getElementById('importFileInput').addEventListener('change', (e) => {
            if (e.target.files[0]) {
                backupManager.importBackup(e.target.files[0]);
            }
        });

        document.getElementById('downloadBackupBtn').addEventListener('click', () => {
            backupManager.openBackupManager();
        });

        // الإعدادات المتقدمة
        document.getElementById('clearCacheBtn').addEventListener('click', () => {
            this.clearCache();
        });

        document.getElementById('resetSettingsBtn').addEventListener('click', () => {
            this.resetSettings();
        });

        document.getElementById('clearAllDataBtn').addEventListener('click', () => {
            this.clearAllData();
        });

        // إضافة عند الضغط على Enter
        ['newViolationType', 'newAbsenceType', 'newRank'].forEach(id => {
            document.getElementById(id).addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    if (id === 'newViolationType') this.addViolationType();
                    else if (id === 'newAbsenceType') this.addAbsenceType();
                    else if (id === 'newRank') this.addRank();
                }
            });
        });
    }

    loadCurrentSettings() {
        const settings = dataManager.data.settings;

        // تحميل الإعدادات العامة
        document.getElementById('maxDelayHours').value = settings.maxDelayHours || 7;
        document.getElementById('maxAbsenceHours').value = settings.maxAbsenceHours || 7;
        document.getElementById('systemTitle').value = settings.systemTitle || 'نظام أرشفة اليوميات العسكرية';
        document.getElementById('organizationName').value = settings.organizationName || '';

        // تحميل القوائم
        this.loadViolationsList();
        this.loadAbsencesList();
        this.loadRanksList();

        // تحميل إعدادات المظهر
        const savedTheme = localStorage.getItem('colorTheme') || 'default';
        const savedFontSize = localStorage.getItem('fontSize') || 'medium';
        const savedAnimations = localStorage.getItem('enableAnimations') !== 'false';

        document.getElementById('colorTheme').value = savedTheme;
        document.getElementById('fontSize').value = savedFontSize;
        document.getElementById('enableAnimations').checked = savedAnimations;
    }

    saveGeneralSettings() {
        const settings = dataManager.data.settings;

        settings.maxDelayHours = parseInt(document.getElementById('maxDelayHours').value);
        settings.maxAbsenceHours = parseInt(document.getElementById('maxAbsenceHours').value);
        settings.systemTitle = document.getElementById('systemTitle').value;
        settings.organizationName = document.getElementById('organizationName').value;

        dataManager.saveData();
        notificationManager.showSuccess('تم حفظ الإعدادات العامة بنجاح');

        // تحديث عنوان الصفحة
        document.querySelector('.header-title').innerHTML = `
            <i class="fas fa-shield-alt"></i>
            ${settings.systemTitle}
        `;
    }

    loadViolationsList() {
        const container = document.getElementById('violationsList');
        const violations = dataManager.data.settings.violationTypes || [];

        container.innerHTML = violations.map((violation, index) => `
            <div class="list-item">
                <span>${violation}</span>
                <button class="btn btn-sm btn-outline-danger" onclick="app.removeViolationType(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `).join('');
    }

    addViolationType() {
        const input = document.getElementById('newViolationType');
        const value = input.value.trim();

        if (!value) {
            notificationManager.showWarning('يرجى إدخال نوع المخالفة');
            return;
        }

        if (dataManager.data.settings.violationTypes.includes(value)) {
            notificationManager.showWarning('نوع المخالفة موجود مسبقاً');
            return;
        }

        dataManager.data.settings.violationTypes.push(value);
        dataManager.saveData();
        input.value = '';
        this.loadViolationsList();
        notificationManager.showSuccess('تم إضافة نوع المخالفة بنجاح');
    }

    removeViolationType(index) {
        notificationManager.showConfirm(
            'هل أنت متأكد من حذف هذا النوع من المخالفات؟',
            'تأكيد الحذف',
            () => {
                dataManager.data.settings.violationTypes.splice(index, 1);
                dataManager.saveData();
                this.loadViolationsList();
                notificationManager.showSuccess('تم حذف نوع المخالفة بنجاح');
            }
        );
    }

    loadAbsencesList() {
        const container = document.getElementById('absencesList');
        const absences = dataManager.data.settings.absenceTypes || [];

        container.innerHTML = absences.map((absence, index) => `
            <div class="list-item">
                <span>${absence}</span>
                <button class="btn btn-sm btn-outline-danger" onclick="app.removeAbsenceType(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `).join('');
    }

    addAbsenceType() {
        const input = document.getElementById('newAbsenceType');
        const value = input.value.trim();

        if (!value) {
            notificationManager.showWarning('يرجى إدخال نوع الغياب');
            return;
        }

        if (dataManager.data.settings.absenceTypes.includes(value)) {
            notificationManager.showWarning('نوع الغياب موجود مسبقاً');
            return;
        }

        dataManager.data.settings.absenceTypes.push(value);
        dataManager.saveData();
        input.value = '';
        this.loadAbsencesList();
        notificationManager.showSuccess('تم إضافة نوع الغياب بنجاح');
    }

    removeAbsenceType(index) {
        notificationManager.showConfirm(
            'هل أنت متأكد من حذف هذا النوع من الغياب؟',
            'تأكيد الحذف',
            () => {
                dataManager.data.settings.absenceTypes.splice(index, 1);
                dataManager.saveData();
                this.loadAbsencesList();
                notificationManager.showSuccess('تم حذف نوع الغياب بنجاح');
            }
        );
    }

    loadRanksList() {
        const container = document.getElementById('ranksList');
        const ranks = dataManager.data.settings.ranks || [];

        container.innerHTML = ranks.map((rank, index) => `
            <div class="list-item">
                <span>${rank}</span>
                <button class="btn btn-sm btn-outline-danger" onclick="app.removeRank(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `).join('');
    }

    addRank() {
        const input = document.getElementById('newRank');
        const value = input.value.trim();

        if (!value) {
            notificationManager.showWarning('يرجى إدخال الرتبة العسكرية');
            return;
        }

        if (dataManager.data.settings.ranks.includes(value)) {
            notificationManager.showWarning('الرتبة موجودة مسبقاً');
            return;
        }

        dataManager.data.settings.ranks.push(value);
        dataManager.saveData();
        input.value = '';
        this.loadRanksList();
        notificationManager.showSuccess('تم إضافة الرتبة بنجاح');
    }

    removeRank(index) {
        notificationManager.showConfirm(
            'هل أنت متأكد من حذف هذه الرتبة؟',
            'تأكيد الحذف',
            () => {
                dataManager.data.settings.ranks.splice(index, 1);
                dataManager.saveData();
                this.loadRanksList();
                notificationManager.showSuccess('تم حذف الرتبة بنجاح');
            }
        );
    }

    saveAppearanceSettings() {
        const theme = document.getElementById('colorTheme').value;
        const fontSize = document.getElementById('fontSize').value;
        const animations = document.getElementById('enableAnimations').checked;

        localStorage.setItem('colorTheme', theme);
        localStorage.setItem('fontSize', fontSize);
        localStorage.setItem('enableAnimations', animations);

        // تطبيق الإعدادات
        document.documentElement.setAttribute('data-theme', theme);
        document.documentElement.setAttribute('data-font-size', fontSize);

        if (!animations) {
            document.documentElement.style.setProperty('--transition-fast', '0s');
            document.documentElement.style.setProperty('--transition-medium', '0s');
            document.documentElement.style.setProperty('--transition-slow', '0s');
        } else {
            document.documentElement.style.removeProperty('--transition-fast');
            document.documentElement.style.removeProperty('--transition-medium');
            document.documentElement.style.removeProperty('--transition-slow');
        }

        notificationManager.showSuccess('تم حفظ إعدادات المظهر بنجاح');
    }

    exportData() {
        try {
            const data = dataManager.exportData();
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `military_archive_backup_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            notificationManager.showSuccess('تم تصدير البيانات بنجاح');
        } catch (error) {
            notificationManager.showError('حدث خطأ أثناء تصدير البيانات: ' + error.message);
        }
    }

    importData(file) {
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const success = dataManager.importData(e.target.result);
                if (success) {
                    notificationManager.showSuccess('تم استيراد البيانات بنجاح');
                    location.reload(); // إعادة تحميل الصفحة لتطبيق البيانات الجديدة
                } else {
                    notificationManager.showError('فشل في استيراد البيانات');
                }
            } catch (error) {
                notificationManager.showError('ملف البيانات غير صالح: ' + error.message);
            }
        };
        reader.readAsText(file);
    }

    downloadBackup() {
        // إنشاء نسخة احتياطية شاملة
        const backupData = {
            data: dataManager.data,
            settings: {
                colorTheme: localStorage.getItem('colorTheme'),
                fontSize: localStorage.getItem('fontSize'),
                enableAnimations: localStorage.getItem('enableAnimations')
            },
            timestamp: new Date().toISOString(),
            version: '1.0'
        };

        const blob = new Blob([JSON.stringify(backupData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `military_archive_full_backup_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        notificationManager.showSuccess('تم تحميل النسخة الاحتياطية الشاملة بنجاح');
    }

    clearCache() {
        notificationManager.showConfirm(
            'هل أنت متأكد من مسح ذاكرة التخزين المؤقت؟',
            'تأكيد المسح',
            () => {
                // مسح ذاكرة التخزين المؤقت
                if ('caches' in window) {
                    caches.keys().then(names => {
                        names.forEach(name => {
                            caches.delete(name);
                        });
                    });
                }

                notificationManager.showSuccess('تم مسح ذاكرة التخزين المؤقت بنجاح');
            }
        );
    }

    resetSettings() {
        notificationManager.showConfirm(
            'هل أنت متأكد من إعادة تعيين جميع الإعدادات؟',
            'تأكيد إعادة التعيين',
            () => {
                // إعادة تعيين الإعدادات للقيم الافتراضية
                dataManager.data.settings = dataManager.getDefaultData().settings;
                dataManager.saveData();

                // إعادة تعيين إعدادات المظهر
                localStorage.removeItem('colorTheme');
                localStorage.removeItem('fontSize');
                localStorage.removeItem('enableAnimations');

                notificationManager.showSuccess('تم إعادة تعيين الإعدادات بنجاح');
                location.reload();
            }
        );
    }

    clearAllData() {
        notificationManager.showConfirm(
            'تحذير: هذا الإجراء سيحذف جميع البيانات نهائياً!\nهل أنت متأكد من المتابعة؟',
            'تأكيد حذف جميع البيانات',
            () => {
                dataManager.clearAllData();
                notificationManager.showSuccess('تم حذف جميع البيانات بنجاح');
                location.reload();
            }
        );
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.app = new MilitaryArchiveApp();
    
    // رسالة ترحيب
    setTimeout(() => {
        notificationManager.showMilitary(
            'مرحباً بك في نظام أرشفة اليوميات العسكرية',
            'مرحباً بك'
        );
    }, 1000);
});
