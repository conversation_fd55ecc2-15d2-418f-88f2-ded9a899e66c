/**
 * الملف الرئيسي لنظام أرشفة اليوميات العسكرية
 * يدير التفاعلات الرئيسية والتنقل بين الصفحات
 */

class MilitaryArchiveApp {
    constructor() {
        this.currentPage = 'dashboard';
        this.sidebarOpen = false;
        this.init();
    }

    /**
     * تهيئة التطبيق
     */
    init() {
        this.setupEventListeners();
        this.loadDashboardStats();
        this.showPage('dashboard');
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // تبديل القائمة الجانبية
        document.getElementById('sidebarToggle').addEventListener('click', () => {
            this.toggleSidebar();
        });

        // التنقل في القائمة الجانبية
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const page = item.dataset.page;
                this.showPage(page);
                this.setActiveMenuItem(item);
                
                // إغلاق القائمة الجانبية في الشاشات الصغيرة
                if (window.innerWidth <= 768) {
                    this.closeSidebar();
                }
            });
        });

        // الإجراءات السريعة
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = btn.dataset.action;
                this.handleQuickAction(action);
            });
        });

        // إغلاق القائمة الجانبية عند النقر خارجها
        document.addEventListener('click', (e) => {
            const sidebar = document.getElementById('sidebar');
            const toggle = document.getElementById('sidebarToggle');
            
            if (this.sidebarOpen && !sidebar.contains(e.target) && !toggle.contains(e.target)) {
                this.closeSidebar();
            }
        });

        // التعامل مع تغيير حجم الشاشة
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768 && this.sidebarOpen) {
                document.getElementById('mainContent').classList.add('sidebar-open');
            } else {
                document.getElementById('mainContent').classList.remove('sidebar-open');
            }
        });
    }

    /**
     * تبديل حالة القائمة الجانبية
     */
    toggleSidebar() {
        if (this.sidebarOpen) {
            this.closeSidebar();
        } else {
            this.openSidebar();
        }
    }

    /**
     * فتح القائمة الجانبية
     */
    openSidebar() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        
        sidebar.classList.add('active');
        if (window.innerWidth > 768) {
            mainContent.classList.add('sidebar-open');
        }
        
        this.sidebarOpen = true;
    }

    /**
     * إغلاق القائمة الجانبية
     */
    closeSidebar() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        
        sidebar.classList.remove('active');
        mainContent.classList.remove('sidebar-open');
        
        this.sidebarOpen = false;
    }

    /**
     * عرض صفحة معينة
     */
    showPage(pageId) {
        // إخفاء جميع الصفحات
        document.querySelectorAll('.page-content').forEach(page => {
            page.classList.remove('active');
        });

        // عرض الصفحة المطلوبة
        const targetPage = document.getElementById(pageId);
        if (targetPage) {
            targetPage.classList.add('active');
            this.currentPage = pageId;
            
            // تحميل محتوى الصفحة
            this.loadPageContent(pageId);
        }
    }

    /**
     * تعيين العنصر النشط في القائمة
     */
    setActiveMenuItem(activeItem) {
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });
        activeItem.classList.add('active');
    }

    /**
     * تحميل محتوى الصفحة
     */
    loadPageContent(pageId) {
        switch (pageId) {
            case 'dashboard':
                this.loadDashboardStats();
                break;
            case 'add-daily':
                this.loadDailyForm();
                break;
            case 'daily-records':
                this.loadDailyRecords();
                break;
            case 'courses':
                this.loadCoursesPage();
                break;
            case 'users':
                this.loadUsersPage();
                break;
            case 'settings':
                this.loadSettingsPage();
                break;
        }
    }

    /**
     * تحميل إحصائيات لوحة التحكم
     */
    loadDashboardStats() {
        document.getElementById('totalViolations').textContent = dataManager.getTotalViolations();
        document.getElementById('totalDelays').textContent = dataManager.getTotalDelays();
        document.getElementById('totalAbsences').textContent = dataManager.getTotalAbsences();
        document.getElementById('totalStudents').textContent = dataManager.getTotalStudents();
    }

    /**
     * تحميل نموذج اليومية
     */
    loadDailyForm() {
        const container = document.querySelector('#add-daily .container-fluid');
        if (container.children.length <= 1) { // فقط العنوان موجود
            const formHTML = formsManager.createDailyForm();
            container.insertAdjacentHTML('beforeend', formHTML);
            this.setupDailyFormEvents();
        }
    }

    /**
     * إعداد أحداث نموذج اليومية
     */
    setupDailyFormEvents() {
        // إضافة طالب جديد
        document.getElementById('addStudentBtn').addEventListener('click', () => {
            this.addStudentToForm();
        });

        // إضافة مرفق
        document.getElementById('addAttachmentBtn').addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });

        // معالجة رفع الملفات
        document.getElementById('fileInput').addEventListener('change', (e) => {
            this.handleFileUpload(e.files);
        });

        // حفظ النموذج
        document.getElementById('dailyForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveDailyRecord();
        });

        // إعادة تعيين النموذج
        document.getElementById('resetFormBtn').addEventListener('click', () => {
            this.resetDailyForm();
        });

        // إضافة طالب افتراضي
        this.addStudentToForm();
    }

    /**
     * إضافة طالب جديد للنموذج
     */
    addStudentToForm() {
        const container = document.getElementById('studentsContainer');
        const index = container.children.length;
        const studentHTML = formsManager.createStudentForm(index);
        
        container.insertAdjacentHTML('beforeend', studentHTML);
        
        const studentCard = container.lastElementChild;
        this.setupStudentCardEvents(studentCard);
    }

    /**
     * إعداد أحداث بطاقة الطالب
     */
    setupStudentCardEvents(studentCard) {
        // إزالة الطالب
        studentCard.querySelector('.remove-student-btn').addEventListener('click', () => {
            if (document.querySelectorAll('.student-card').length > 1) {
                studentCard.remove();
                notificationManager.showInfo('تم حذف الطالب');
            } else {
                notificationManager.showWarning('يجب وجود طالب واحد على الأقل');
            }
        });

        // البحث التلقائي للطلاب
        formsManager.setupStudentSearch(studentCard);
        
        // البحث التلقائي للدورات
        formsManager.setupCourseSearch(studentCard);

        // إضافة مخالفة
        studentCard.querySelector('.add-violation-btn').addEventListener('click', () => {
            this.addViolation(studentCard);
        });

        // عرض/إخفاء ساعات الغياب
        studentCard.querySelector('.student-absence-type').addEventListener('change', (e) => {
            const absenceHoursRow = studentCard.querySelector('.absence-hours-row');
            if (e.target.value && e.target.value !== '') {
                absenceHoursRow.style.display = 'block';
            } else {
                absenceHoursRow.style.display = 'none';
            }
        });
    }

    /**
     * إضافة مخالفة للطالب
     */
    addViolation(studentCard) {
        const select = studentCard.querySelector('.violation-select');
        const violationsList = studentCard.querySelector('.violations-list');
        
        if (select.value) {
            const violationItem = document.createElement('div');
            violationItem.className = 'violation-item badge bg-danger me-2 mb-2';
            violationItem.innerHTML = `
                ${select.value}
                <button type="button" class="btn-close btn-close-white ms-2" onclick="this.parentElement.remove()"></button>
            `;
            
            violationsList.appendChild(violationItem);
            select.value = '';
            
            notificationManager.showMilitary('تم إضافة مخالفة جديدة', 'إضافة مخالفة');
        }
    }

    /**
     * معالجة رفع الملفات
     */
    handleFileUpload(files) {
        const container = document.getElementById('attachmentsContainer');
        
        Array.from(files).forEach(file => {
            if (file.size > 10 * 1024 * 1024) { // 10MB
                notificationManager.showError('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت');
                return;
            }

            const attachmentItem = document.createElement('div');
            attachmentItem.className = 'attachment-item';
            attachmentItem.innerHTML = `
                <div class="card mb-2">
                    <div class="card-body p-2">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file me-2"></i>
                            <span class="flex-grow-1">${file.name}</span>
                            <small class="text-muted me-2">${this.formatFileSize(file.size)}</small>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="this.closest('.attachment-item').remove()">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            container.appendChild(attachmentItem);
        });

        notificationManager.showSuccess('تم إضافة المرفقات بنجاح');
    }

    /**
     * تنسيق حجم الملف
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * حفظ سجل اليومية
     */
    saveDailyRecord() {
        this.showLoading(true);
        
        try {
            const formData = this.collectDailyFormData();
            const record = dataManager.addDailyRecord(formData);
            
            notificationManager.showSuccess('تم حفظ اليومية بنجاح');
            this.resetDailyForm();
            this.loadDashboardStats();
            
        } catch (error) {
            notificationManager.showError('حدث خطأ أثناء حفظ اليومية: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * جمع بيانات نموذج اليومية
     */
    collectDailyFormData() {
        const date = document.getElementById('dailyDate').value;
        const supervisor = document.getElementById('supervisor').value;
        const students = [];

        document.querySelectorAll('.student-card').forEach(card => {
            const violations = Array.from(card.querySelectorAll('.violation-item'))
                .map(item => item.textContent.trim().replace('×', ''));

            const student = {
                name: card.querySelector('.student-name').value,
                militaryNumber: card.querySelector('.student-military-number').value,
                rank: card.querySelector('.student-rank').value,
                courseCode: card.querySelector('.student-course-code').value,
                courseNumber: card.querySelector('.student-course-number').value,
                courseName: card.querySelector('.student-course-name').value,
                violations: violations,
                delays: parseInt(card.querySelector('.student-delays').value) || 0,
                absenceType: card.querySelector('.student-absence-type').value,
                absenceHours: parseInt(card.querySelector('.student-absence-hours').value) || 0
            };

            students.push(student);
        });

        return {
            date: date,
            supervisor: supervisor,
            students: students,
            attachments: [] // سيتم تطوير نظام المرفقات لاحقاً
        };
    }

    /**
     * إعادة تعيين نموذج اليومية
     */
    resetDailyForm() {
        formsManager.resetForm('dailyForm');
        document.getElementById('studentsContainer').innerHTML = '';
        document.getElementById('attachmentsContainer').innerHTML = '';
        this.addStudentToForm();
        
        // إعادة تعيين التاريخ للتاريخ الحالي
        document.getElementById('dailyDate').value = new Date().toISOString().split('T')[0];
    }

    /**
     * معالجة الإجراءات السريعة
     */
    handleQuickAction(action) {
        switch (action) {
            case 'add-daily':
                this.showPage('add-daily');
                this.setActiveMenuItem(document.querySelector('[data-page="add-daily"]'));
                break;
            case 'view-records':
                this.showPage('daily-records');
                this.setActiveMenuItem(document.querySelector('[data-page="daily-records"]'));
                break;
            case 'add-course':
                this.showAddCourseModal();
                break;
        }
    }

    /**
     * عرض نافذة إضافة دورة
     */
    showAddCourseModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-graduation-cap"></i> إضافة دورة جديدة
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${formsManager.createCourseForm()}
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);
        
        // معالجة حفظ الدورة
        modal.querySelector('#courseForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveCourse(modal, bootstrapModal);
        });

        bootstrapModal.show();
    }

    /**
     * حفظ الدورة
     */
    saveCourse(modal, bootstrapModal) {
        try {
            const formData = {
                courseCode: modal.querySelector('#courseCode').value,
                courseNumber: modal.querySelector('#courseNumber').value,
                courseName: modal.querySelector('#courseName').value,
                startDate: modal.querySelector('#startDate').value,
                endDate: modal.querySelector('#endDate').value
            };

            dataManager.addCourse(formData);
            notificationManager.showSuccess('تم إضافة الدورة بنجاح');
            bootstrapModal.hide();
            
        } catch (error) {
            notificationManager.showError('حدث خطأ: ' + error.message);
        }
    }

    /**
     * عرض/إخفاء شاشة التحميل
     */
    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        overlay.style.display = show ? 'flex' : 'none';
    }

    // ===== صفحة السجلات =====
    loadDailyRecords() {
        const container = document.querySelector('#daily-records .container-fluid');
        if (container.children.length <= 1) {
            const recordsHTML = this.createDailyRecordsPage();
            container.insertAdjacentHTML('beforeend', recordsHTML);
            this.setupRecordsPageEvents();
            this.loadRecordsList();
        }
    }

    createDailyRecordsPage() {
        return `
            <div class="records-page">
                <div class="filters-section mb-4">
                    <div class="info-card">
                        <h5><i class="fas fa-filter"></i> فلترة السجلات</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">التاريخ</label>
                                    <input type="date" class="form-control" id="filterDate">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">المشرف</label>
                                    <input type="text" class="form-control" id="filterSupervisor" placeholder="اسم المشرف">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">اسم الطالب</label>
                                    <input type="text" class="form-control" id="filterStudent" placeholder="اسم الطالب">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-primary" id="applyFilters">
                                            <i class="fas fa-search"></i> بحث
                                        </button>
                                        <button class="btn btn-secondary" id="clearFilters">
                                            <i class="fas fa-times"></i> مسح
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="records-list">
                    <div class="table-container">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5><i class="fas fa-list"></i> قائمة اليوميات</h5>
                            <span class="badge bg-primary" id="recordsCount">0 سجل</span>
                        </div>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>المشرف</th>
                                        <th>عدد الطلاب</th>
                                        <th>المخالفات</th>
                                        <th>التأخيرات</th>
                                        <th>الغيابات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="recordsTableBody">
                                    <!-- سيتم إضافة السجلات هنا -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupRecordsPageEvents() {
        // تطبيق الفلاتر
        document.getElementById('applyFilters').addEventListener('click', () => {
            this.applyRecordsFilters();
        });

        // مسح الفلاتر
        document.getElementById('clearFilters').addEventListener('click', () => {
            this.clearRecordsFilters();
        });

        // البحث التلقائي
        ['filterDate', 'filterSupervisor', 'filterStudent'].forEach(id => {
            document.getElementById(id).addEventListener('input', () => {
                this.applyRecordsFilters();
            });
        });
    }

    loadRecordsList() {
        const records = dataManager.getAllDailyRecords();
        this.displayRecords(records);
    }

    applyRecordsFilters() {
        const filters = {
            date: document.getElementById('filterDate').value,
            supervisor: document.getElementById('filterSupervisor').value,
            studentName: document.getElementById('filterStudent').value
        };

        const filteredRecords = dataManager.searchDailyRecords(filters);
        this.displayRecords(filteredRecords);
    }

    clearRecordsFilters() {
        document.getElementById('filterDate').value = '';
        document.getElementById('filterSupervisor').value = '';
        document.getElementById('filterStudent').value = '';
        this.loadRecordsList();
    }

    displayRecords(records) {
        const tbody = document.getElementById('recordsTableBody');
        const countBadge = document.getElementById('recordsCount');

        countBadge.textContent = `${records.length} سجل`;

        if (records.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-2x mb-2"></i><br>
                        لا توجد سجلات مطابقة للبحث
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = records.map(record => {
            const totalViolations = record.students.reduce((sum, student) =>
                sum + (student.violations ? student.violations.length : 0), 0);
            const totalDelays = record.students.reduce((sum, student) =>
                sum + (student.delays || 0), 0);
            const totalAbsences = record.students.reduce((sum, student) =>
                sum + (student.absenceHours || 0), 0);

            return `
                <tr class="table-row">
                    <td>${dataManager.formatDate(record.date)}</td>
                    <td>${record.supervisor}</td>
                    <td>${record.students.length}</td>
                    <td>
                        <span class="badge bg-danger">${totalViolations}</span>
                    </td>
                    <td>
                        <span class="badge bg-warning">${totalDelays}</span>
                    </td>
                    <td>
                        <span class="badge bg-info">${totalAbsences}</span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="app.viewRecordDetails('${record.id}')">
                            <i class="fas fa-eye"></i> عرض
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    viewRecordDetails(recordId) {
        const record = dataManager.data.dailyRecords.find(r => r.id === recordId);
        if (!record) return;

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-file-alt"></i> تفاصيل اليومية - ${dataManager.formatDate(record.date)}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>التاريخ:</strong> ${dataManager.formatDate(record.date)}
                            </div>
                            <div class="col-md-6">
                                <strong>المشرف:</strong> ${record.supervisor}
                            </div>
                        </div>

                        <h6><i class="fas fa-users"></i> قائمة الطلاب</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>الرقم العسكري</th>
                                        <th>الرتبة</th>
                                        <th>الدورة</th>
                                        <th>المخالفات</th>
                                        <th>التأخيرات</th>
                                        <th>الغياب</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${record.students.map(student => `
                                        <tr>
                                            <td>${student.name}</td>
                                            <td>${student.militaryNumber}</td>
                                            <td>${student.rank}</td>
                                            <td>${student.courseName}</td>
                                            <td>
                                                ${student.violations && student.violations.length > 0
                                                    ? student.violations.map(v => `<span class="badge bg-danger me-1">${v}</span>`).join('')
                                                    : '<span class="text-muted">لا يوجد</span>'
                                                }
                                            </td>
                                            <td>
                                                ${student.delays > 0
                                                    ? `<span class="badge bg-warning">${student.delays} حصة</span>`
                                                    : '<span class="text-muted">لا يوجد</span>'
                                                }
                                            </td>
                                            <td>
                                                ${student.absenceType
                                                    ? `<span class="badge bg-info">${student.absenceType} (${student.absenceHours} ساعة)</span>`
                                                    : '<span class="text-muted">لا يوجد</span>'
                                                }
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);

        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });

        bootstrapModal.show();
    }

    // ===== صفحة الدورات =====
    loadCoursesPage() {
        const container = document.querySelector('#courses .container-fluid');
        if (container.children.length <= 1) {
            const coursesHTML = this.createCoursesPage();
            container.insertAdjacentHTML('beforeend', coursesHTML);
            this.setupCoursesPageEvents();
            this.loadCoursesList();
        }
    }

    createCoursesPage() {
        return `
            <div class="courses-page">
                <div class="row">
                    <div class="col-md-8">
                        <div class="table-container">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5><i class="fas fa-graduation-cap"></i> قائمة الدورات</h5>
                                <button class="btn btn-primary" id="addCourseBtn">
                                    <i class="fas fa-plus"></i> إضافة دورة جديدة
                                </button>
                            </div>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>رمز الدورة</th>
                                            <th>رقم الدورة</th>
                                            <th>اسم الدورة</th>
                                            <th>تاريخ البداية</th>
                                            <th>تاريخ النهاية</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="coursesTableBody">
                                        <!-- سيتم إضافة الدورات هنا -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-card">
                            <h5><i class="fas fa-chart-pie"></i> إحصائيات الدورات</h5>
                            <div class="stats-list">
                                <div class="stat-item">
                                    <span class="stat-label">إجمالي الدورات:</span>
                                    <span class="stat-value" id="totalCoursesCount">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">الدورات النشطة:</span>
                                    <span class="stat-value" id="activeCoursesCount">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">الدورات المكتملة:</span>
                                    <span class="stat-value" id="completedCoursesCount">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupCoursesPageEvents() {
        document.getElementById('addCourseBtn').addEventListener('click', () => {
            this.showAddCourseModal();
        });
    }

    loadCoursesList() {
        const courses = dataManager.getAllCourses();
        this.displayCourses(courses);
        this.updateCoursesStats(courses);
    }

    displayCourses(courses) {
        const tbody = document.getElementById('coursesTableBody');

        if (courses.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center text-muted py-4">
                        <i class="fas fa-graduation-cap fa-2x mb-2"></i><br>
                        لا توجد دورات مسجلة
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = courses.map(course => `
            <tr class="table-row">
                <td>${course.courseCode}</td>
                <td>${course.courseNumber}</td>
                <td>${course.courseName}</td>
                <td>${dataManager.formatDate(course.startDate)}</td>
                <td>${dataManager.formatDate(course.endDate)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="app.editCourse('${course.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="app.deleteCourse('${course.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    updateCoursesStats(courses) {
        const today = new Date();
        const activeCourses = courses.filter(course =>
            new Date(course.startDate) <= today && new Date(course.endDate) >= today
        );
        const completedCourses = courses.filter(course =>
            new Date(course.endDate) < today
        );

        document.getElementById('totalCoursesCount').textContent = courses.length;
        document.getElementById('activeCoursesCount').textContent = activeCourses.length;
        document.getElementById('completedCoursesCount').textContent = completedCourses.length;
    }

    editCourse(courseId) {
        // سيتم تطوير وظيفة التعديل
        notificationManager.showInfo('وظيفة التعديل قيد التطوير');
    }

    deleteCourse(courseId) {
        notificationManager.showConfirm(
            'هل أنت متأكد من حذف هذه الدورة؟',
            'تأكيد الحذف',
            () => {
                // سيتم تطوير وظيفة الحذف
                notificationManager.showSuccess('تم حذف الدورة بنجاح');
                this.loadCoursesList();
            }
        );
    }

    // الصفحات الأخرى - سيتم تطويرها لاحقاً
    loadUsersPage() {
        const container = document.querySelector('#users .container-fluid');
        if (container.children.length <= 1) {
            container.insertAdjacentHTML('beforeend', `
                <div class="info-card text-center">
                    <h5><i class="fas fa-users"></i> إدارة المستخدمين</h5>
                    <p class="text-muted">هذه الصفحة قيد التطوير</p>
                    <i class="fas fa-tools fa-3x text-muted"></i>
                </div>
            `);
        }
    }

    loadSettingsPage() {
        const container = document.querySelector('#settings .container-fluid');
        if (container.children.length <= 1) {
            container.insertAdjacentHTML('beforeend', `
                <div class="info-card text-center">
                    <h5><i class="fas fa-cog"></i> الإعدادات</h5>
                    <p class="text-muted">هذه الصفحة قيد التطوير</p>
                    <i class="fas fa-tools fa-3x text-muted"></i>
                </div>
            `);
        }
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.app = new MilitaryArchiveApp();
    
    // رسالة ترحيب
    setTimeout(() => {
        notificationManager.showMilitary(
            'مرحباً بك في نظام أرشفة اليوميات العسكرية',
            'مرحباً بك'
        );
    }, 1000);
});
