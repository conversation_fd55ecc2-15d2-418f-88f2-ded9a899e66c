# دليل البدء السريع - نظام أرشفة اليوميات العسكرية

## 🚀 البدء السريع (5 دقائق)

### 1. تشغيل النظام
```
1. انقر نقراً مزدوجاً على ملف index.html
2. سيفتح النظام في متصفحك تلقائياً
3. ستظهر رسالة ترحيب وبيانات تجريبية
```

### 2. إضافة يومية جديدة
```
1. انقر على "إضافة اليومية" من القائمة الجانبية
2. أدخل التاريخ واسم المشرف
3. أضف بيانات الطلاب:
   - اكتب الاسم أو الرقم العسكري (سيظهر البحث التلقائي)
   - اختر الرتبة من القائمة
   - أدخل رمز ورقم الدورة (سيتم ملء الاسم تلقائياً)
   - أضف المخالفات والتأخيرات والغيابات حسب الحاجة
4. انقر "حفظ اليومية"
```

### 3. عرض السجلات السابقة
```
1. انقر على "سجل اليوميات السابقة"
2. استخدم الفلاتر للبحث (التاريخ، المشرف، الطالب)
3. انقر على "عرض" لرؤية تفاصيل أي سجل
```

### 4. إدارة الدورات
```
1. انقر على "إدارة الدورات"
2. انقر "إضافة دورة جديدة"
3. أدخل بيانات الدورة الكاملة
4. احفظ الدورة
```

## 🎯 نصائح سريعة

### البحث التلقائي
- **للطلاب**: اكتب حرفين من الاسم أو 3 أرقام من الرقم العسكري
- **للدورات**: أدخل رمز ورقم الدورة وسيظهر الاسم تلقائياً

### إضافة المخالفات
- اختر نوع المخالفة من القائمة المنسدلة
- انقر "إضافة مخالفة" لإضافتها للقائمة
- يمكن إضافة عدة مخالفات لنفس الطالب

### التأخيرات والغيابات
- **التأخيرات**: أدخل عدد الحصص (حد أقصى 7)
- **الغيابات**: اختر نوع الغياب أولاً، ثم أدخل عدد الساعات

### المرفقات
- انقر "إضافة مرفق" لرفع الملفات
- الأنواع المدعومة: PDF, JPG, PNG, DOC, DOCX
- الحد الأقصى: 10 ميجابايت لكل ملف

## ⚡ اختصارات لوحة المفاتيح

| الاختصار | الوظيفة |
|---------|---------|
| `Ctrl + N` | إضافة يومية جديدة |
| `Ctrl + S` | حفظ النموذج الحالي |
| `Ctrl + F` | البحث في السجلات |
| `Esc` | إغلاق النوافذ المنبثقة |

## 🔧 حل المشاكل السريع

### المشكلة: البيانات لا تُحفظ
**الحل**: تأكد من تمكين JavaScript في المتصفح

### المشكلة: البحث التلقائي لا يعمل
**الحل**: تأكد من وجود بيانات طلاب أو دورات مسبقاً

### المشكلة: التصميم لا يظهر بشكل صحيح
**الحل**: تأكد من اتصال الإنترنت لتحميل Bootstrap و Font Awesome

### المشكلة: الخطوط العربية لا تظهر
**الحل**: تأكد من تحميل خط Cairo من Google Fonts

## 📱 الاستخدام على الهاتف

النظام متجاوب ويعمل على الهواتف:
- القائمة الجانبية تصبح منبثقة
- الجداول قابلة للتمرير أفقياً
- الأزرار تصبح أكبر للمس السهل

## 💾 النسخ الاحتياطي السريع

### إنشاء نسخة احتياطية
```
1. انسخ مجلد "data" بالكامل
2. احتفظ بنسخة من ملف index.html
3. احفظ النسخة في مكان آمن
```

### استعادة النسخة الاحتياطية
```
1. استبدل مجلد "data" بالنسخة المحفوظة
2. أعد تحميل الصفحة
3. ستظهر البيانات المحفوظة
```

## 🎨 التخصيص السريع

### تغيير ألوان النظام
عدّل في ملف `css/main.css`:
```css
:root {
    --military-navy: #1a365d;    /* اللون الأساسي */
    --military-gold: #d69e2e;    /* اللون الذهبي */
}
```

### إضافة أنواع مخالفات جديدة
عدّل في ملف `data/data.json`:
```json
"violationTypes": [
    "عدم ارتداء الزي الرسمي",
    "مخالفة جديدة هنا"
]
```

## 📊 فهم الإحصائيات

### لوحة التحكم
- **المخالفات**: إجمالي المخالفات في جميع اليوميات
- **التأخيرات**: إجمالي ساعات التأخير
- **الغيابات**: إجمالي ساعات الغياب
- **الطلاب**: عدد الطلاب المسجلين في النظام

### ألوان الحالة
- 🔴 **أحمر**: مخالفات
- 🟡 **أصفر**: تأخيرات
- 🔵 **أزرق**: غيابات
- 🟢 **أخضر**: بيانات عامة

## 🔒 الأمان والخصوصية

- ✅ البيانات محفوظة محلياً فقط
- ✅ لا يتم إرسال أي بيانات للإنترنت
- ✅ يمكن استخدام النظام بدون اتصال
- ✅ التشفير المحلي للبيانات الحساسة

## 📞 الحصول على المساعدة

### الأخطاء الشائعة
1. **خطأ JavaScript**: أعد تحميل الصفحة
2. **بيانات مفقودة**: تحقق من مجلد data
3. **تصميم مكسور**: تحقق من اتصال الإنترنت

### نصائح الأداء
- استخدم متصفح حديث (Chrome, Firefox, Safari, Edge)
- أغلق التبويبات الأخرى لتحسين الأداء
- امسح ذاكرة التخزين المؤقت إذا واجهت مشاكل

---

**🎖️ تم تصميم هذا النظام خصيصاً للاستخدام العسكري مع مراعاة أعلى معايير الأمان والكفاءة**
