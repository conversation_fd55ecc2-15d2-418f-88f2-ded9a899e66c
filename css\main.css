/* ===== نظام أرشفة اليوميات العسكرية - التصميم الرئيسي ===== */

/* ===== المتغيرات العامة ===== */
:root {
    /* الألوان العسكرية */
    --military-navy: #1a365d;
    --military-blue: #2c5282;
    --military-gold: #d69e2e;
    --military-silver: #a0aec0;
    --military-dark: #1a202c;
    --military-light: #f7fafc;
    --military-gray: #4a5568;
    
    /* ألوان الحالة */
    --success-color: #38a169;
    --warning-color: #d69e2e;
    --danger-color: #e53e3e;
    --info-color: #3182ce;
    
    /* الخطوط */
    --main-font: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    
    /* الظلال */
    --shadow-light: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 8px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 16px rgba(0,0,0,0.2);
    
    /* الانتقالات */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ===== الإعدادات العامة ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--main-font);
    background: linear-gradient(135deg, var(--military-light) 0%, #e2e8f0 100%);
    color: var(--military-dark);
    line-height: 1.6;
    overflow-x: hidden;
}

/* ===== الهيدر العسكري ===== */
.military-header {
    background: linear-gradient(135deg, var(--military-navy) 0%, var(--military-blue) 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: var(--shadow-heavy);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    border-bottom: 3px solid var(--military-gold);
}

.header-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header-title i {
    color: var(--military-gold);
    margin-left: 10px;
}

.user-info {
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-info i {
    font-size: 1.5rem;
    color: var(--military-gold);
}

.sidebar-toggle {
    border: 2px solid var(--military-gold);
    color: var(--military-gold);
    transition: var(--transition-fast);
}

.sidebar-toggle:hover {
    background-color: var(--military-gold);
    color: var(--military-navy);
    transform: scale(1.05);
}

/* ===== القائمة الجانبية ===== */
.sidebar {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100vh;
    background: linear-gradient(180deg, var(--military-dark) 0%, var(--military-gray) 100%);
    transition: var(--transition-medium);
    z-index: 1001;
    box-shadow: var(--shadow-heavy);
    padding-top: 80px;
}

.sidebar.active {
    right: 0;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 2px solid var(--military-gold);
    background: rgba(0,0,0,0.2);
}

.sidebar-header h3 {
    color: var(--military-gold);
    font-size: 1.3rem;
    font-weight: 600;
    text-align: center;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.menu-item {
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.menu-item a {
    display: block;
    padding: 1rem 1.5rem;
    color: white;
    text-decoration: none;
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.menu-item a:hover,
.menu-item.active a {
    background: linear-gradient(90deg, var(--military-gold) 0%, rgba(214, 158, 46, 0.8) 100%);
    color: var(--military-dark);
    transform: translateX(-5px);
}

.menu-item a i {
    margin-left: 10px;
    width: 20px;
    text-align: center;
}

/* ===== المحتوى الرئيسي ===== */
.main-content {
    margin-top: 80px;
    padding: 2rem;
    transition: var(--transition-medium);
}

.main-content.sidebar-open {
    margin-right: 300px;
}

.page-content {
    display: none;
    animation: fadeInUp 0.5s ease;
}

.page-content.active {
    display: block;
}

/* ===== البانر الترحيبي ===== */
.welcome-banner {
    background: linear-gradient(135deg, var(--military-blue) 0%, var(--military-navy) 100%);
    color: white;
    padding: 3rem 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-heavy);
    position: relative;
    overflow: hidden;
}

.welcome-banner::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(214, 158, 46, 0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

.banner-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.banner-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.banner-content h2 i {
    color: var(--military-gold);
    margin-left: 15px;
}

.banner-content p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* ===== بطاقات الإحصائيات ===== */
.stats-cards {
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--shadow-medium);
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
    border-top: 4px solid;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.stat-card.violations {
    border-top-color: var(--danger-color);
}

.stat-card.delays {
    border-top-color: var(--warning-color);
}

.stat-card.absences {
    border-top-color: var(--info-color);
}

.stat-card.students {
    border-top-color: var(--success-color);
}

.card-icon {
    position: absolute;
    top: 1rem;
    left: 1rem;
    font-size: 2.5rem;
    opacity: 0.2;
}

.violations .card-icon {
    color: var(--danger-color);
}

.delays .card-icon {
    color: var(--warning-color);
}

.absences .card-icon {
    color: var(--info-color);
}

.students .card-icon {
    color: var(--success-color);
}

.card-content {
    text-align: center;
    position: relative;
    z-index: 2;
}

.card-content h3 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--military-dark);
}

.card-content p {
    font-size: 1.1rem;
    color: var(--military-gray);
    margin: 0;
}

/* ===== الإجراءات السريعة ===== */
.quick-actions {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--shadow-medium);
}

.quick-actions h3 {
    color: var(--military-dark);
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.quick-actions h3 i {
    color: var(--military-gold);
    margin-left: 10px;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.action-btn {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 10px;
    transition: var(--transition-fast);
    border: none;
    min-width: 200px;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.action-btn i {
    margin-left: 8px;
}

/* ===== رؤوس الصفحات ===== */
.page-header {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-medium);
    border-right: 5px solid var(--military-gold);
}

.page-header h2 {
    color: var(--military-dark);
    font-size: 2rem;
    font-weight: 600;
    margin: 0;
}

.page-header h2 i {
    color: var(--military-gold);
    margin-left: 15px;
}

/* ===== حاوي الإشعارات ===== */
.notification-container {
    position: fixed;
    top: 100px;
    left: 20px;
    z-index: 2000;
    max-width: 400px;
}

/* ===== شاشة التحميل ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(26, 54, 93, 0.9);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 3000;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.loading-spinner i {
    font-size: 3rem;
    color: var(--military-gold);
    margin-bottom: 1rem;
}

.loading-spinner p {
    font-size: 1.2rem;
    margin: 0;
}

/* ===== تصميم الإشعارات ===== */
.notification {
    background: white;
    border-radius: 10px;
    box-shadow: var(--shadow-heavy);
    margin-bottom: 1rem;
    overflow: hidden;
    transform: translateX(100%);
    transition: var(--transition-medium);
    max-width: 400px;
    border-right: 4px solid;
}

.notification.show {
    transform: translateX(0);
}

.notification.removing {
    transform: translateX(100%);
    opacity: 0;
}

.notification-success {
    border-right-color: var(--success-color);
}

.notification-error {
    border-right-color: var(--danger-color);
}

.notification-warning {
    border-right-color: var(--warning-color);
}

.notification-info {
    border-right-color: var(--info-color);
}

.notification-military {
    border-right-color: var(--military-gold);
}

.notification-content {
    padding: 1rem;
}

.notification-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.notification-icon {
    font-size: 1.2rem;
    margin-left: 10px;
    width: 24px;
    text-align: center;
}

.notification-success .notification-icon {
    color: var(--success-color);
}

.notification-error .notification-icon {
    color: var(--danger-color);
}

.notification-warning .notification-icon {
    color: var(--warning-color);
}

.notification-info .notification-icon {
    color: var(--info-color);
}

.notification-military .notification-icon {
    color: var(--military-gold);
}

.notification-title {
    font-weight: 600;
    flex: 1;
    color: var(--military-dark);
}

.notification-close {
    background: none;
    border: none;
    color: var(--military-gray);
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.notification-close:hover {
    background: rgba(0,0,0,0.1);
    color: var(--military-dark);
}

.notification-message {
    color: var(--military-gray);
    line-height: 1.5;
    margin-bottom: 0.5rem;
}

.notification-progress {
    height: 3px;
    background: rgba(0,0,0,0.1);
    margin: 0 -1rem -1rem -1rem;
    position: relative;
    overflow: hidden;
}

.notification-progress::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 100%;
    background: currentColor;
    animation: progressBar linear;
    transform-origin: right;
}

.notification-success .notification-progress::before {
    background: var(--success-color);
}

.notification-error .notification-progress::before {
    background: var(--danger-color);
}

.notification-warning .notification-progress::before {
    background: var(--warning-color);
}

.notification-info .notification-progress::before {
    background: var(--info-color);
}

.notification-military .notification-progress::before {
    background: var(--military-gold);
}

@keyframes progressBar {
    from {
        transform: scaleX(1);
    }
    to {
        transform: scaleX(0);
    }
}

/* ===== تصميم البانرات ===== */
.alert-banner {
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    z-index: 1500;
    padding: 1rem 0;
    transform: translateY(-100%);
    transition: var(--transition-medium);
    border-bottom: 3px solid;
}

.alert-banner.show {
    transform: translateY(0);
}

.alert-banner.removing {
    transform: translateY(-100%);
}

.alert-banner-success {
    background: var(--success-color);
    border-bottom-color: #2f855a;
    color: white;
}

.alert-banner-error {
    background: var(--danger-color);
    border-bottom-color: #c53030;
    color: white;
}

.alert-banner-warning {
    background: var(--warning-color);
    border-bottom-color: #b7791f;
    color: white;
}

.alert-banner-info {
    background: var(--info-color);
    border-bottom-color: #2c5282;
    color: white;
}

.alert-banner-military {
    background: var(--military-gold);
    border-bottom-color: #b7791f;
    color: var(--military-dark);
}

.alert-banner-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.alert-banner-icon {
    font-size: 1.5rem;
}

.alert-banner-message {
    font-size: 1.1rem;
    font-weight: 600;
    flex: 1;
    text-align: center;
}

.alert-banner-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: var(--transition-fast);
    opacity: 0.8;
}

.alert-banner-close:hover {
    opacity: 1;
    background: rgba(0,0,0,0.1);
}

/* ===== الاستجابة للشاشات الصغيرة ===== */
@media (max-width: 768px) {
    .header-title {
        font-size: 1.4rem;
    }

    .sidebar {
        width: 280px;
        right: -280px;
    }

    .main-content.sidebar-open {
        margin-right: 0;
    }

    .banner-content h2 {
        font-size: 2rem;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .action-btn {
        min-width: 250px;
    }

    .card-content h3 {
        font-size: 2.5rem;
    }

    .notification {
        max-width: calc(100% - 20px);
        margin: 0 10px 1rem 10px;
    }

    .alert-banner-content {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .alert-banner-message {
        font-size: 1rem;
    }
}

/* ===== تصميم النماذج ===== */
.daily-form-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--shadow-medium);
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--military-gold);
}

.section-header h4 {
    color: var(--military-dark);
    font-weight: 600;
    margin: 0;
}

.section-header h4 i {
    color: var(--military-gold);
    margin-left: 10px;
}

.students-container {
    max-height: 600px;
    overflow-y: auto;
    padding-right: 10px;
}

.student-card {
    margin-bottom: 1.5rem;
    animation: fadeInUp 0.5s ease;
}

.student-card .card {
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    transition: var(--transition-medium);
}

.student-card .card:hover {
    border-color: var(--military-gold);
    box-shadow: var(--shadow-medium);
}

.student-card .card-header {
    background: linear-gradient(135deg, var(--military-blue) 0%, var(--military-navy) 100%);
    color: white;
    border-radius: 8px 8px 0 0;
    padding: 1rem;
}

.student-card .card-header h5 {
    margin: 0;
    font-weight: 600;
}

.student-card .card-header h5 i {
    color: var(--military-gold);
    margin-left: 10px;
}

.remove-student-btn {
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    transition: var(--transition-fast);
}

.remove-student-btn:hover {
    background: rgba(229, 62, 62, 0.8);
    border-color: #e53e3e;
    color: white;
}

.form-group label {
    font-weight: 600;
    color: var(--military-dark);
    margin-bottom: 0.5rem;
}

.form-control {
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 0.75rem;
    transition: var(--transition-fast);
    font-size: 1rem;
}

.form-control:focus {
    border-color: var(--military-gold);
    box-shadow: 0 0 0 0.2rem rgba(214, 158, 46, 0.25);
}

.form-control.is-valid {
    border-color: var(--success-color);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 0 0 8px 8px;
    box-shadow: var(--shadow-medium);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
}

.suggestion-item {
    padding: 0.75rem;
    cursor: pointer;
    border-bottom: 1px solid #f1f5f9;
    transition: var(--transition-fast);
}

.suggestion-item:hover {
    background: var(--military-light);
}

.suggestion-item:last-child {
    border-bottom: none;
}

.violations-container {
    position: relative;
}

.violations-list {
    min-height: 40px;
    padding: 0.5rem;
    border: 1px dashed #cbd5e0;
    border-radius: 8px;
    background: #f8fafc;
}

.violation-item {
    display: inline-flex;
    align-items: center;
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
    margin: 0.25rem;
    border-radius: 20px;
    position: relative;
}

.violation-item .btn-close {
    font-size: 0.7rem;
    margin-right: 0.5rem;
}

.attachments-container {
    min-height: 100px;
    border: 2px dashed #cbd5e0;
    border-radius: 10px;
    padding: 1rem;
    background: #f8fafc;
    text-align: center;
    transition: var(--transition-fast);
}

.attachments-container:hover {
    border-color: var(--military-gold);
    background: #fefcf3;
}

.attachment-item {
    text-align: right;
}

.attachment-item .card {
    border: 1px solid #e2e8f0;
    transition: var(--transition-fast);
}

.attachment-item .card:hover {
    border-color: var(--military-gold);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

.form-actions .btn {
    min-width: 150px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 10px;
    transition: var(--transition-fast);
}

.form-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* ===== تصميم الجداول ===== */
.table-container {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: var(--shadow-medium);
    overflow: hidden;
}

.table {
    margin: 0;
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    background: linear-gradient(135deg, var(--military-blue) 0%, var(--military-navy) 100%);
    color: white;
    font-weight: 600;
    padding: 1rem;
    border: none;
    text-align: center;
    position: relative;
}

.table thead th:first-child {
    border-radius: 10px 0 0 0;
}

.table thead th:last-child {
    border-radius: 0 10px 0 0;
}

.table tbody td {
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
    vertical-align: middle;
    text-align: center;
}

.table tbody tr {
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background: var(--military-light);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* ===== تصميم البطاقات ===== */
.info-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: var(--shadow-medium);
    margin-bottom: 1.5rem;
    border-top: 4px solid var(--military-gold);
    transition: var(--transition-medium);
}

.info-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
}

.info-card h5 {
    color: var(--military-dark);
    font-weight: 600;
    margin-bottom: 1rem;
}

.info-card h5 i {
    color: var(--military-gold);
    margin-left: 10px;
}

/* ===== تصميم الأزرار المخصصة ===== */
.btn-military {
    background: linear-gradient(135deg, var(--military-blue) 0%, var(--military-navy) 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: var(--transition-fast);
}

.btn-military:hover {
    background: linear-gradient(135deg, var(--military-navy) 0%, var(--military-dark) 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-gold {
    background: var(--military-gold);
    border: none;
    color: var(--military-dark);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: var(--transition-fast);
}

.btn-gold:hover {
    background: #b7791f;
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* ===== تحسينات إضافية ===== */
.fade-in {
    animation: fadeIn 0.5s ease;
}

.slide-in-right {
    animation: slideInRight 0.5s ease;
}

.slide-in-left {
    animation: slideInLeft 0.5s ease;
}

.zoom-in {
    animation: zoomIn 0.3s ease;
}

.pulse-glow {
    animation: pulse 2s infinite;
}

/* ===== تخصيص شريط التمرير ===== */
.students-container::-webkit-scrollbar {
    width: 8px;
}

.students-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.students-container::-webkit-scrollbar-thumb {
    background: var(--military-gold);
    border-radius: 4px;
}

.students-container::-webkit-scrollbar-thumb:hover {
    background: #b7791f;
}

/* ===== تصميم الإحصائيات ===== */
.stats-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    font-weight: 500;
    color: var(--military-gray);
}

.stat-value {
    font-weight: 700;
    font-size: 1.2rem;
    color: var(--military-dark);
    background: var(--military-gold);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    min-width: 40px;
    text-align: center;
}

/* ===== تحسينات إضافية للجداول ===== */
.table-responsive {
    border-radius: 10px;
    overflow: hidden;
}

.table tbody tr:nth-child(even) {
    background-color: rgba(248, 250, 252, 0.5);
}

.table .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
}

/* ===== تصميم الفلاتر ===== */
.filters-section .form-group {
    margin-bottom: 1rem;
}

.filters-section .form-label {
    font-weight: 600;
    color: var(--military-dark);
    margin-bottom: 0.5rem;
}

.filters-section .btn {
    width: 100%;
    font-weight: 600;
}

/* ===== تحسينات الاستجابة ===== */
@media (max-width: 992px) {
    .filters-section .col-md-3 {
        margin-bottom: 1rem;
    }

    .table-container {
        padding: 1rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .student-card .card-header h5 {
        font-size: 1rem;
    }

    .remove-student-btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }

    .violation-item {
        font-size: 0.8rem;
        padding: 0.3rem 0.5rem;
    }

    .table {
        font-size: 0.9rem;
    }

    .table thead th,
    .table tbody td {
        padding: 0.5rem;
    }
}

/* ===== تأثيرات خاصة ===== */
.highlight-new {
    animation: highlightNew 2s ease;
}

@keyframes highlightNew {
    0% {
        background-color: rgba(214, 158, 46, 0.3);
    }
    100% {
        background-color: transparent;
    }
}

.error-shake {
    animation: shake 0.5s ease;
}

.success-bounce {
    animation: bounce 0.6s ease;
}

/* ===== تحسينات الطباعة ===== */
@media print {
    .sidebar,
    .military-header,
    .notification-container,
    .loading-overlay,
    .btn,
    .form-actions {
        display: none !important;
    }

    .main-content {
        margin-top: 0 !important;
        margin-right: 0 !important;
    }

    .page-content {
        box-shadow: none !important;
        border: none !important;
    }

    .table {
        border-collapse: collapse !important;
    }

    .table th,
    .table td {
        border: 1px solid #000 !important;
        padding: 0.5rem !important;
    }
}

/* ===== تحسينات الوصولية ===== */
.btn:focus,
.form-control:focus {
    outline: 2px solid var(--military-gold);
    outline-offset: 2px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== تحسينات الأداء ===== */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

.no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* ===== متغيرات CSS مخصصة للثيمات ===== */
[data-theme="dark"] {
    --military-light: #2d3748;
    --military-dark: #f7fafc;
    --military-gray: #a0aec0;
}

[data-theme="dark"] body {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    color: #f7fafc;
}

[data-theme="dark"] .info-card,
[data-theme="dark"] .table-container,
[data-theme="dark"] .daily-form-container {
    background: #4a5568;
    color: #f7fafc;
}

/* ===== تحسينات أخيرة ===== */
.loading-dots::after {
    content: '';
    animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
    0%, 20% {
        content: '.';
    }
    40% {
        content: '..';
    }
    60%, 100% {
        content: '...';
    }
}
