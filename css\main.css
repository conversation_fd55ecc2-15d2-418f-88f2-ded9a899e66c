/* ===== نظام أرشفة اليوميات العسكرية - التصميم الرئيسي ===== */

/* ===== المتغيرات العامة ===== */
:root {
    /* الألوان العسكرية */
    --military-navy: #1a365d;
    --military-blue: #2c5282;
    --military-gold: #d69e2e;
    --military-silver: #a0aec0;
    --military-dark: #1a202c;
    --military-light: #f7fafc;
    --military-gray: #4a5568;
    
    /* ألوان الحالة */
    --success-color: #38a169;
    --warning-color: #d69e2e;
    --danger-color: #e53e3e;
    --info-color: #3182ce;
    
    /* الخطوط */
    --main-font: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    
    /* الظلال */
    --shadow-light: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 8px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 16px rgba(0,0,0,0.2);
    
    /* الانتقالات */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ===== الإعدادات العامة ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--main-font);
    background: linear-gradient(135deg, var(--military-light) 0%, #e2e8f0 100%);
    color: var(--military-dark);
    line-height: 1.6;
    overflow-x: hidden;
}

/* ===== الهيدر العسكري ===== */
.military-header {
    background: linear-gradient(135deg, var(--military-navy) 0%, var(--military-blue) 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: var(--shadow-heavy);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    border-bottom: 3px solid var(--military-gold);
}

.header-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header-title i {
    color: var(--military-gold);
    margin-left: 10px;
}

.user-info {
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-info i {
    font-size: 1.5rem;
    color: var(--military-gold);
}

.sidebar-toggle {
    border: 2px solid var(--military-gold);
    color: var(--military-gold);
    transition: var(--transition-fast);
}

.sidebar-toggle:hover {
    background-color: var(--military-gold);
    color: var(--military-navy);
    transform: scale(1.05);
}

/* ===== القائمة الجانبية ===== */
.sidebar {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100vh;
    background: linear-gradient(180deg, var(--military-dark) 0%, var(--military-gray) 100%);
    transition: var(--transition-medium);
    z-index: 1001;
    box-shadow: var(--shadow-heavy);
    padding-top: 80px;
}

.sidebar.active {
    right: 0;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 2px solid var(--military-gold);
    background: rgba(0,0,0,0.2);
}

.sidebar-header h3 {
    color: var(--military-gold);
    font-size: 1.3rem;
    font-weight: 600;
    text-align: center;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.menu-item {
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.menu-item a {
    display: block;
    padding: 1rem 1.5rem;
    color: white;
    text-decoration: none;
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.menu-item a:hover,
.menu-item.active a {
    background: linear-gradient(90deg, var(--military-gold) 0%, rgba(214, 158, 46, 0.8) 100%);
    color: var(--military-dark);
    transform: translateX(-5px);
}

.menu-item a i {
    margin-left: 10px;
    width: 20px;
    text-align: center;
}

/* ===== المحتوى الرئيسي ===== */
.main-content {
    margin-top: 80px;
    padding: 2rem;
    transition: var(--transition-medium);
}

.main-content.sidebar-open {
    margin-right: 300px;
}

.page-content {
    display: none;
    animation: fadeInUp 0.5s ease;
}

.page-content.active {
    display: block;
}

/* ===== البانر الترحيبي ===== */
.welcome-banner {
    background: linear-gradient(135deg, var(--military-blue) 0%, var(--military-navy) 100%);
    color: white;
    padding: 3rem 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-heavy);
    position: relative;
    overflow: hidden;
}

.welcome-banner::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(214, 158, 46, 0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

.banner-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.banner-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.banner-content h2 i {
    color: var(--military-gold);
    margin-left: 15px;
}

.banner-content p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* ===== بطاقات الإحصائيات ===== */
.stats-cards {
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--shadow-medium);
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
    border-top: 4px solid;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.stat-card.violations {
    border-top-color: var(--danger-color);
}

.stat-card.delays {
    border-top-color: var(--warning-color);
}

.stat-card.absences {
    border-top-color: var(--info-color);
}

.stat-card.students {
    border-top-color: var(--success-color);
}

.card-icon {
    position: absolute;
    top: 1rem;
    left: 1rem;
    font-size: 2.5rem;
    opacity: 0.2;
}

.violations .card-icon {
    color: var(--danger-color);
}

.delays .card-icon {
    color: var(--warning-color);
}

.absences .card-icon {
    color: var(--info-color);
}

.students .card-icon {
    color: var(--success-color);
}

.card-content {
    text-align: center;
    position: relative;
    z-index: 2;
}

.card-content h3 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--military-dark);
}

.card-content p {
    font-size: 1.1rem;
    color: var(--military-gray);
    margin: 0;
}

/* ===== الإجراءات السريعة ===== */
.quick-actions {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--shadow-medium);
}

.quick-actions h3 {
    color: var(--military-dark);
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.quick-actions h3 i {
    color: var(--military-gold);
    margin-left: 10px;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.action-btn {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 10px;
    transition: var(--transition-fast);
    border: none;
    min-width: 200px;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.action-btn i {
    margin-left: 8px;
}

/* ===== رؤوس الصفحات ===== */
.page-header {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-medium);
    border-right: 5px solid var(--military-gold);
}

.page-header h2 {
    color: var(--military-dark);
    font-size: 2rem;
    font-weight: 600;
    margin: 0;
}

.page-header h2 i {
    color: var(--military-gold);
    margin-left: 15px;
}

/* ===== حاوي الإشعارات ===== */
.notification-container {
    position: fixed;
    top: 100px;
    left: 20px;
    z-index: 2000;
    max-width: 400px;
}

/* ===== شاشة التحميل ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(26, 54, 93, 0.9);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 3000;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.loading-spinner i {
    font-size: 3rem;
    color: var(--military-gold);
    margin-bottom: 1rem;
}

.loading-spinner p {
    font-size: 1.2rem;
    margin: 0;
}

/* ===== تصميم الإشعارات ===== */
.notification {
    background: white;
    border-radius: 10px;
    box-shadow: var(--shadow-heavy);
    margin-bottom: 1rem;
    overflow: hidden;
    transform: translateX(100%);
    transition: var(--transition-medium);
    max-width: 400px;
    border-right: 4px solid;
}

.notification.show {
    transform: translateX(0);
}

.notification.removing {
    transform: translateX(100%);
    opacity: 0;
}

.notification-success {
    border-right-color: var(--success-color);
}

.notification-error {
    border-right-color: var(--danger-color);
}

.notification-warning {
    border-right-color: var(--warning-color);
}

.notification-info {
    border-right-color: var(--info-color);
}

.notification-military {
    border-right-color: var(--military-gold);
}

.notification-content {
    padding: 1rem;
}

.notification-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.notification-icon {
    font-size: 1.2rem;
    margin-left: 10px;
    width: 24px;
    text-align: center;
}

.notification-success .notification-icon {
    color: var(--success-color);
}

.notification-error .notification-icon {
    color: var(--danger-color);
}

.notification-warning .notification-icon {
    color: var(--warning-color);
}

.notification-info .notification-icon {
    color: var(--info-color);
}

.notification-military .notification-icon {
    color: var(--military-gold);
}

.notification-title {
    font-weight: 600;
    flex: 1;
    color: var(--military-dark);
}

.notification-close {
    background: none;
    border: none;
    color: var(--military-gray);
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.notification-close:hover {
    background: rgba(0,0,0,0.1);
    color: var(--military-dark);
}

.notification-message {
    color: var(--military-gray);
    line-height: 1.5;
    margin-bottom: 0.5rem;
}

.notification-progress {
    height: 3px;
    background: rgba(0,0,0,0.1);
    margin: 0 -1rem -1rem -1rem;
    position: relative;
    overflow: hidden;
}

.notification-progress::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 100%;
    background: currentColor;
    animation: progressBar linear;
    transform-origin: right;
}

.notification-success .notification-progress::before {
    background: var(--success-color);
}

.notification-error .notification-progress::before {
    background: var(--danger-color);
}

.notification-warning .notification-progress::before {
    background: var(--warning-color);
}

.notification-info .notification-progress::before {
    background: var(--info-color);
}

.notification-military .notification-progress::before {
    background: var(--military-gold);
}

@keyframes progressBar {
    from {
        transform: scaleX(1);
    }
    to {
        transform: scaleX(0);
    }
}

/* ===== تصميم البانرات ===== */
.alert-banner {
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    z-index: 1500;
    padding: 1rem 0;
    transform: translateY(-100%);
    transition: var(--transition-medium);
    border-bottom: 3px solid;
}

.alert-banner.show {
    transform: translateY(0);
}

.alert-banner.removing {
    transform: translateY(-100%);
}

.alert-banner-success {
    background: var(--success-color);
    border-bottom-color: #2f855a;
    color: white;
}

.alert-banner-error {
    background: var(--danger-color);
    border-bottom-color: #c53030;
    color: white;
}

.alert-banner-warning {
    background: var(--warning-color);
    border-bottom-color: #b7791f;
    color: white;
}

.alert-banner-info {
    background: var(--info-color);
    border-bottom-color: #2c5282;
    color: white;
}

.alert-banner-military {
    background: var(--military-gold);
    border-bottom-color: #b7791f;
    color: var(--military-dark);
}

.alert-banner-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.alert-banner-icon {
    font-size: 1.5rem;
}

.alert-banner-message {
    font-size: 1.1rem;
    font-weight: 600;
    flex: 1;
    text-align: center;
}

.alert-banner-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: var(--transition-fast);
    opacity: 0.8;
}

.alert-banner-close:hover {
    opacity: 1;
    background: rgba(0,0,0,0.1);
}

/* ===== الاستجابة للشاشات الصغيرة ===== */
@media (max-width: 768px) {
    .header-title {
        font-size: 1.4rem;
    }

    .sidebar {
        width: 280px;
        right: -280px;
    }

    .main-content.sidebar-open {
        margin-right: 0;
    }

    .banner-content h2 {
        font-size: 2rem;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .action-btn {
        min-width: 250px;
    }

    .card-content h3 {
        font-size: 2.5rem;
    }

    .notification {
        max-width: calc(100% - 20px);
        margin: 0 10px 1rem 10px;
    }

    .alert-banner-content {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .alert-banner-message {
        font-size: 1rem;
    }
}

/* ===== تصميم النماذج ===== */
.daily-form-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: var(--shadow-medium);
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--military-gold);
}

.section-header h4 {
    color: var(--military-dark);
    font-weight: 600;
    margin: 0;
}

.section-header h4 i {
    color: var(--military-gold);
    margin-left: 10px;
}

.attachment-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.attachment-buttons .btn {
    font-weight: 600;
    border-radius: 8px;
    transition: var(--transition-fast);
}

.attachment-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.attachment-buttons .btn i {
    margin-left: 8px;
}

.students-container {
    max-height: 600px;
    overflow-y: auto;
    padding-right: 10px;
}

.student-card {
    margin-bottom: 1.5rem;
    animation: fadeInUp 0.5s ease;
}

.student-card .card {
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    transition: var(--transition-medium);
}

.student-card .card:hover {
    border-color: var(--military-gold);
    box-shadow: var(--shadow-medium);
}

.student-card .card-header {
    background: linear-gradient(135deg, var(--military-blue) 0%, var(--military-navy) 100%);
    color: white;
    border-radius: 8px 8px 0 0;
    padding: 1rem;
}

.student-card .card-header h5 {
    margin: 0;
    font-weight: 600;
}

.student-card .card-header h5 i {
    color: var(--military-gold);
    margin-left: 10px;
}

.remove-student-btn {
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    transition: var(--transition-fast);
}

.remove-student-btn:hover {
    background: rgba(229, 62, 62, 0.8);
    border-color: #e53e3e;
    color: white;
}

.form-group label {
    font-weight: 600;
    color: var(--military-dark);
    margin-bottom: 0.5rem;
}

.form-control {
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 0.75rem;
    transition: var(--transition-fast);
    font-size: 1rem;
}

.form-control:focus {
    border-color: var(--military-gold);
    box-shadow: 0 0 0 0.2rem rgba(214, 158, 46, 0.25);
}

.form-control.is-valid {
    border-color: var(--success-color);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 0 0 8px 8px;
    box-shadow: var(--shadow-medium);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
}

.suggestion-item {
    padding: 0.75rem;
    cursor: pointer;
    border-bottom: 1px solid #f1f5f9;
    transition: var(--transition-fast);
}

.suggestion-item:hover {
    background: var(--military-light);
}

.suggestion-item:last-child {
    border-bottom: none;
}

.violations-container {
    position: relative;
}

.violations-list {
    min-height: 40px;
    padding: 0.5rem;
    border: 1px dashed #cbd5e0;
    border-radius: 8px;
    background: #f8fafc;
}

.violation-item {
    display: inline-flex;
    align-items: center;
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
    margin: 0.25rem;
    border-radius: 20px;
    position: relative;
}

.violation-item .btn-close {
    font-size: 0.7rem;
    margin-right: 0.5rem;
}

.attachments-container {
    min-height: 100px;
    border: 2px dashed #cbd5e0;
    border-radius: 10px;
    padding: 1rem;
    background: #f8fafc;
    text-align: center;
    transition: var(--transition-fast);
}

.attachments-container:hover {
    border-color: var(--military-gold);
    background: #fefcf3;
}

.attachment-item {
    text-align: right;
}

.attachment-item .card {
    border: 1px solid #e2e8f0;
    transition: var(--transition-fast);
}

.attachment-item .card:hover {
    border-color: var(--military-gold);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

.form-actions .btn {
    min-width: 150px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 10px;
    transition: var(--transition-fast);
}

.form-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* ===== تصميم الجداول ===== */
.table-container {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: var(--shadow-medium);
    overflow: hidden;
}

.table {
    margin: 0;
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    background: linear-gradient(135deg, var(--military-blue) 0%, var(--military-navy) 100%);
    color: white;
    font-weight: 600;
    padding: 1rem;
    border: none;
    text-align: center;
    position: relative;
}

.table thead th:first-child {
    border-radius: 10px 0 0 0;
}

.table thead th:last-child {
    border-radius: 0 10px 0 0;
}

.table tbody td {
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
    vertical-align: middle;
    text-align: center;
}

.table tbody tr {
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background: var(--military-light);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* ===== تصميم البطاقات ===== */
.info-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: var(--shadow-medium);
    margin-bottom: 1.5rem;
    border-top: 4px solid var(--military-gold);
    transition: var(--transition-medium);
}

.info-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
}

.info-card h5 {
    color: var(--military-dark);
    font-weight: 600;
    margin-bottom: 1rem;
}

.info-card h5 i {
    color: var(--military-gold);
    margin-left: 10px;
}

/* ===== تصميم الأزرار المخصصة ===== */
.btn-military {
    background: linear-gradient(135deg, var(--military-blue) 0%, var(--military-navy) 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: var(--transition-fast);
}

.btn-military:hover {
    background: linear-gradient(135deg, var(--military-navy) 0%, var(--military-dark) 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-gold {
    background: var(--military-gold);
    border: none;
    color: var(--military-dark);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: var(--transition-fast);
}

.btn-gold:hover {
    background: #b7791f;
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* ===== تحسينات إضافية ===== */
.fade-in {
    animation: fadeIn 0.5s ease;
}

.slide-in-right {
    animation: slideInRight 0.5s ease;
}

.slide-in-left {
    animation: slideInLeft 0.5s ease;
}

.zoom-in {
    animation: zoomIn 0.3s ease;
}

.pulse-glow {
    animation: pulse 2s infinite;
}

/* ===== تخصيص شريط التمرير ===== */
.students-container::-webkit-scrollbar {
    width: 8px;
}

.students-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.students-container::-webkit-scrollbar-thumb {
    background: var(--military-gold);
    border-radius: 4px;
}

.students-container::-webkit-scrollbar-thumb:hover {
    background: #b7791f;
}

/* ===== تصميم الإحصائيات ===== */
.stats-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    font-weight: 500;
    color: var(--military-gray);
}

.stat-value {
    font-weight: 700;
    font-size: 1.2rem;
    color: var(--military-dark);
    background: var(--military-gold);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    min-width: 40px;
    text-align: center;
}

/* ===== تحسينات إضافية للجداول ===== */
.table-responsive {
    border-radius: 10px;
    overflow: hidden;
}

.table tbody tr:nth-child(even) {
    background-color: rgba(248, 250, 252, 0.5);
}

.table .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
}

/* ===== تصميم الفلاتر ===== */
.filters-section .form-group {
    margin-bottom: 1rem;
}

.filters-section .form-label {
    font-weight: 600;
    color: var(--military-dark);
    margin-bottom: 0.5rem;
}

.filters-section .btn {
    width: 100%;
    font-weight: 600;
}

/* ===== تحسينات الاستجابة ===== */
@media (max-width: 992px) {
    .filters-section .col-md-3 {
        margin-bottom: 1rem;
    }

    .table-container {
        padding: 1rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .student-card .card-header h5 {
        font-size: 1rem;
    }

    .remove-student-btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }

    .violation-item {
        font-size: 0.8rem;
        padding: 0.3rem 0.5rem;
    }

    .table {
        font-size: 0.9rem;
    }

    .table thead th,
    .table tbody td {
        padding: 0.5rem;
    }
}

/* ===== تأثيرات خاصة ===== */
.highlight-new {
    animation: highlightNew 2s ease;
}

@keyframes highlightNew {
    0% {
        background-color: rgba(214, 158, 46, 0.3);
    }
    100% {
        background-color: transparent;
    }
}

.error-shake {
    animation: shake 0.5s ease;
}

.success-bounce {
    animation: bounce 0.6s ease;
}

/* ===== تحسينات الطباعة ===== */
@media print {
    .sidebar,
    .military-header,
    .notification-container,
    .loading-overlay,
    .btn,
    .form-actions {
        display: none !important;
    }

    .main-content {
        margin-top: 0 !important;
        margin-right: 0 !important;
    }

    .page-content {
        box-shadow: none !important;
        border: none !important;
    }

    .table {
        border-collapse: collapse !important;
    }

    .table th,
    .table td {
        border: 1px solid #000 !important;
        padding: 0.5rem !important;
    }
}

/* ===== تحسينات الوصولية ===== */
.btn:focus,
.form-control:focus {
    outline: 2px solid var(--military-gold);
    outline-offset: 2px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== تحسينات الأداء ===== */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

.no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* ===== متغيرات CSS مخصصة للثيمات ===== */
[data-theme="dark"] {
    --military-light: #2d3748;
    --military-dark: #f7fafc;
    --military-gray: #a0aec0;
}

[data-theme="dark"] body {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    color: #f7fafc;
}

[data-theme="dark"] .info-card,
[data-theme="dark"] .table-container,
[data-theme="dark"] .daily-form-container {
    background: #4a5568;
    color: #f7fafc;
}

/* ===== تحسينات أخيرة ===== */
.loading-dots::after {
    content: '';
    animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
    0%, 20% {
        content: '.';
    }
    40% {
        content: '..';
    }
    60%, 100% {
        content: '...';
    }
}

/* ===== تصميم صفحة المستخدمين ===== */
.users-page .role-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.users-page .role-item:last-child {
    border-bottom: none;
}

.users-page .roles-info {
    background: #f8fafc;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

/* ===== تصميم صفحة الإعدادات ===== */
.settings-page .list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    transition: var(--transition-fast);
}

.settings-page .list-item:hover {
    background: #e2e8f0;
    border-color: var(--military-gold);
}

.settings-page .list-item span {
    flex: 1;
    font-weight: 500;
}

.settings-page .violations-list,
.settings-page .absences-list,
.settings-page .ranks-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 0.5rem;
    background: white;
}

.settings-page .form-check {
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.settings-page .form-check:hover {
    background: #e2e8f0;
}

.settings-page .form-check-input:checked {
    background-color: var(--military-gold);
    border-color: var(--military-gold);
}

/* ===== تحسينات النماذج ===== */
.modal-lg .form-check {
    background: #f8fafc;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: var(--transition-fast);
}

.modal-lg .form-check:hover {
    background: #e2e8f0;
    border-color: var(--military-gold);
}

.modal-lg .form-check-input:checked {
    background-color: var(--military-gold);
    border-color: var(--military-gold);
}

.modal-lg .form-check-label {
    font-weight: 500;
    cursor: pointer;
}

/* ===== تصميم الأزرار المتقدمة ===== */
.btn-outline-info:hover {
    background-color: var(--info-color);
    border-color: var(--info-color);
}

.btn-outline-warning:hover {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-outline-danger:hover {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

/* ===== تحسينات الجداول المتقدمة ===== */
.table .badge {
    font-size: 0.75rem;
    padding: 0.35rem 0.65rem;
    font-weight: 600;
}

.table tbody tr:hover .badge {
    transform: scale(1.05);
    transition: var(--transition-fast);
}

/* ===== تصميم قوائم الإعدادات ===== */
.settings-page .input-group {
    margin-bottom: 1rem;
}

.settings-page .input-group .form-control {
    border-right: none;
}

.settings-page .input-group .btn {
    border-left: none;
    border-color: #e2e8f0;
}

.settings-page .input-group .btn:hover {
    border-color: var(--military-gold);
    background-color: var(--military-gold);
    color: white;
}

/* ===== تحسينات الاستجابة للصفحات الجديدة ===== */
@media (max-width: 768px) {
    .users-page .table,
    .settings-page .table {
        font-size: 0.85rem;
    }

    .users-page .table th,
    .users-page .table td,
    .settings-page .table th,
    .settings-page .table td {
        padding: 0.5rem 0.25rem;
    }

    .settings-page .list-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .settings-page .list-item .btn {
        align-self: flex-end;
    }

    .modal-lg .row .col-md-6 {
        margin-bottom: 1rem;
    }
}

/* ===== تأثيرات خاصة للإعدادات ===== */
.settings-page .info-card {
    transition: var(--transition-medium);
}

.settings-page .info-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.settings-page .btn:active {
    transform: scale(0.98);
}

/* ===== تحسينات إضافية للمظهر ===== */
[data-theme="dark"] .settings-page .list-item,
[data-theme="dark"] .settings-page .form-check {
    background: #4a5568;
    border-color: #718096;
    color: #f7fafc;
}

[data-theme="dark"] .settings-page .list-item:hover,
[data-theme="dark"] .settings-page .form-check:hover {
    background: #2d3748;
    border-color: var(--military-gold);
}

[data-font-size="small"] {
    font-size: 0.9rem;
}

[data-font-size="large"] {
    font-size: 1.1rem;
}

[data-font-size="large"] .table {
    font-size: 1rem;
}

[data-font-size="small"] .table {
    font-size: 0.8rem;
}

/* ===== تحسينات النسخ الاحتياطي ===== */
.settings-page .d-grid .btn {
    margin-bottom: 0.5rem;
    font-weight: 600;
    transition: var(--transition-fast);
}

.settings-page .d-grid .btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
}

.settings-page .d-grid .btn i {
    margin-left: 8px;
}

/* ===== تحسينات أخيرة ===== */
.fade-in-up {
    animation: fadeInUp 0.6s ease;
}

.slide-in-down {
    animation: slideInDown 0.5s ease;
}

.bounce-in {
    animation: bounceIn 0.8s ease;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* ===== تصميم الماسح الضوئي ===== */
.scanner-modal .modal-dialog {
    max-width: 95%;
    margin: 1rem auto;
}

.scanner-container {
    background: #000;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
}

.camera-view {
    position: relative;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #1a1a1a;
}

#scannerVideo {
    width: 100%;
    height: auto;
    max-height: 500px;
    object-fit: cover;
    border-radius: 10px;
}

.scanner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    transition: var(--transition-fast);
}

.scan-frame {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    height: 60%;
    border: 2px dashed var(--military-gold);
    border-radius: 10px;
}

.scan-frame .corner {
    position: absolute;
    width: 30px;
    height: 30px;
    border: 3px solid var(--military-gold);
}

.scan-frame .corner.top-left {
    top: -3px;
    left: -3px;
    border-right: none;
    border-bottom: none;
    border-radius: 10px 0 0 0;
}

.scan-frame .corner.top-right {
    top: -3px;
    right: -3px;
    border-left: none;
    border-bottom: none;
    border-radius: 0 10px 0 0;
}

.scan-frame .corner.bottom-left {
    bottom: -3px;
    left: -3px;
    border-right: none;
    border-top: none;
    border-radius: 0 0 0 10px;
}

.scan-frame .corner.bottom-right {
    bottom: -3px;
    right: -3px;
    border-left: none;
    border-top: none;
    border-radius: 0 0 10px 0;
}

.scanner-instructions {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 0.9rem;
    text-align: center;
}

.scanner-instructions i {
    color: var(--military-gold);
    margin-left: 8px;
}

.scanner-controls {
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--military-dark) 0%, var(--military-gray) 100%);
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.scanner-controls .btn {
    min-width: 120px;
    font-weight: 600;
    border-radius: 25px;
    transition: var(--transition-fast);
}

.scanner-controls .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.scanner-controls .btn i {
    margin-left: 8px;
}

.scanner-controls .btn.active {
    background: var(--military-gold);
    border-color: var(--military-gold);
    color: var(--military-dark);
}

/* ===== قائمة الصور الملتقطة ===== */
.captured-images {
    background: white;
    border-radius: 15px;
    padding: 1rem;
    box-shadow: var(--shadow-medium);
    height: 100%;
}

.captured-images h6 {
    color: var(--military-dark);
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--military-gold);
}

.captured-images h6 i {
    color: var(--military-gold);
    margin-left: 8px;
}

.images-list {
    max-height: 300px;
    overflow-y: auto;
    padding-right: 5px;
}

.no-images {
    text-align: center;
    padding: 2rem 1rem;
}

.captured-image-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    transition: var(--transition-fast);
}

.captured-image-item:hover {
    background: #e2e8f0;
    border-color: var(--military-gold);
    transform: translateX(-3px);
}

.captured-thumbnail {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #e2e8f0;
    margin-left: 10px;
}

.image-info {
    flex: 1;
}

.image-name {
    display: block;
    font-weight: 600;
    color: var(--military-dark);
    margin-bottom: 0.25rem;
}

.image-actions {
    display: flex;
    gap: 0.25rem;
}

.image-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* ===== إعدادات المسح ===== */
.scan-settings {
    background: white;
    border-radius: 15px;
    padding: 1rem;
    box-shadow: var(--shadow-medium);
}

.scan-settings h6 {
    color: var(--military-dark);
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--military-gold);
}

.scan-settings h6 i {
    color: var(--military-gold);
    margin-left: 8px;
}

.scan-settings .form-control {
    font-size: 0.9rem;
    padding: 0.5rem;
}

.scan-settings .form-check {
    background: #f8fafc;
    padding: 0.75rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    margin-bottom: 0;
}

.scan-settings .form-check:hover {
    background: #e2e8f0;
    border-color: var(--military-gold);
}

.scan-settings .form-check-input:checked {
    background-color: var(--military-gold);
    border-color: var(--military-gold);
}

/* ===== خطأ الكاميرا ===== */
.camera-error {
    text-align: center;
    padding: 3rem 2rem;
    color: white;
}

.camera-error h5 {
    color: white;
    margin-bottom: 1rem;
}

.camera-error ul {
    text-align: right;
    display: inline-block;
    margin-bottom: 2rem;
}

.camera-error .btn {
    background: var(--military-gold);
    border-color: var(--military-gold);
    color: var(--military-dark);
    font-weight: 600;
}

/* ===== تحسينات الاستجابة للماسح الضوئي ===== */
@media (max-width: 768px) {
    .scanner-modal .modal-dialog {
        max-width: 98%;
        margin: 0.5rem auto;
    }

    .scanner-modal .row {
        flex-direction: column-reverse;
    }

    .scanner-modal .col-md-4 {
        margin-bottom: 1rem;
    }

    .camera-view {
        min-height: 300px;
    }

    .scan-frame {
        width: 90%;
        height: 70%;
    }

    .scanner-controls {
        padding: 1rem;
        gap: 0.5rem;
    }

    .scanner-controls .btn {
        min-width: 100px;
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }

    .captured-images,
    .scan-settings {
        margin-bottom: 1rem;
    }

    .images-list {
        max-height: 200px;
    }

    .captured-image-item {
        padding: 0.5rem;
    }

    .captured-thumbnail {
        width: 50px;
        height: 50px;
    }

    .scanner-instructions {
        font-size: 0.8rem;
        padding: 8px 15px;
        bottom: 10px;
    }
}

/* ===== تأثيرات خاصة للماسح الضوئي ===== */
.scan-frame {
    animation: scanPulse 2s ease-in-out infinite;
}

@keyframes scanPulse {
    0%, 100% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.02);
    }
}

.scanner-controls .btn:active {
    transform: translateY(0) scale(0.95);
}

.captured-image-item {
    animation: slideInRight 0.3s ease;
}

/* ===== تخصيص شريط التمرير للصور ===== */
.images-list::-webkit-scrollbar {
    width: 6px;
}

.images-list::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.images-list::-webkit-scrollbar-thumb {
    background: var(--military-gold);
    border-radius: 3px;
}

.images-list::-webkit-scrollbar-thumb:hover {
    background: #b7791f;
}

/* ===== تصميم المرفقات المتقدمة ===== */
.attachments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    padding: 1rem;
}

.attachment-card {
    background: white;
    border-radius: 10px;
    box-shadow: var(--shadow-light);
    transition: var(--transition-medium);
    overflow: hidden;
    border: 2px solid transparent;
}

.attachment-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--military-gold);
}

.attachment-preview {
    position: relative;
    height: 150px;
    background: #f8fafc;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    overflow: hidden;
}

.attachment-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-medium);
}

.attachment-preview:hover .attachment-thumbnail {
    transform: scale(1.05);
}

.attachment-icon {
    color: var(--military-gray);
    transition: var(--transition-fast);
}

.attachment-preview:hover .attachment-icon {
    color: var(--military-gold);
    transform: scale(1.1);
}

.attachment-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(26, 54, 93, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-fast);
}

.attachment-overlay i {
    color: white;
    font-size: 2rem;
}

.attachment-preview:hover .attachment-overlay {
    opacity: 1;
}

.attachment-info {
    padding: 1rem;
}

.attachment-name {
    font-weight: 600;
    color: var(--military-dark);
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.attachment-size {
    font-size: 0.9rem;
    color: var(--military-gray);
    margin-bottom: 0.75rem;
}

.attachment-actions {
    display: flex;
    gap: 0.25rem;
    justify-content: center;
}

.attachment-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    border-radius: 5px;
}

.no-attachments {
    text-align: center;
    padding: 3rem 2rem;
    border: 2px dashed #cbd5e0;
    border-radius: 10px;
    background: #f8fafc;
}

/* ===== عارض المرفقات ===== */
.attachment-viewer-modal .modal-content {
    background: #1a1a1a;
    color: white;
    border: none;
}

.attachment-viewer-modal .modal-header {
    border-bottom: 1px solid #333;
    background: #2d2d2d;
}

.attachment-viewer-modal .modal-title {
    flex: 1;
    font-size: 1.1rem;
}

.viewer-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.attachment-counter {
    font-size: 0.9rem;
    color: #ccc;
    padding: 0 1rem;
}

.viewer-container {
    position: relative;
    height: calc(100vh - 120px);
    display: flex;
    flex-direction: column;
}

.viewer-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: auto;
    background: #000;
    position: relative;
}

.viewer-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
    cursor: grab;
}

.viewer-image:active {
    cursor: grabbing;
}

.viewer-pdf {
    width: 100%;
    height: 100%;
    border: none;
}

.viewer-unsupported {
    text-align: center;
    color: #ccc;
    padding: 3rem;
}

.viewer-toolbar {
    background: #2d2d2d;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #333;
}

.zoom-controls,
.viewer-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.zoom-level {
    min-width: 60px;
    text-align: center;
    font-weight: 600;
    color: var(--military-gold);
}

.viewer-toolbar .btn {
    background: transparent;
    border: 1px solid #555;
    color: #ccc;
    padding: 0.5rem;
    border-radius: 5px;
    transition: var(--transition-fast);
}

.viewer-toolbar .btn:hover {
    background: var(--military-gold);
    border-color: var(--military-gold);
    color: var(--military-dark);
}

.viewer-toolbar .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* ===== تحسينات الاستجابة للمرفقات ===== */
@media (max-width: 768px) {
    .attachments-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 0.75rem;
        padding: 0.75rem;
    }

    .attachment-preview {
        height: 120px;
    }

    .attachment-info {
        padding: 0.75rem;
    }

    .attachment-actions {
        flex-direction: column;
        gap: 0.25rem;
    }

    .attachment-actions .btn {
        width: 100%;
        font-size: 0.75rem;
    }

    .viewer-controls {
        gap: 0.5rem;
    }

    .attachment-counter {
        font-size: 0.8rem;
        padding: 0 0.5rem;
    }

    .viewer-toolbar {
        flex-direction: column;
        gap: 1rem;
        padding: 0.75rem;
    }

    .zoom-controls,
    .viewer-actions {
        width: 100%;
        justify-content: center;
    }
}

/* ===== تأثيرات خاصة للمرفقات ===== */
.attachment-card {
    animation: fadeInUp 0.5s ease;
}

.attachment-card:nth-child(1) { animation-delay: 0.1s; }
.attachment-card:nth-child(2) { animation-delay: 0.2s; }
.attachment-card:nth-child(3) { animation-delay: 0.3s; }
.attachment-card:nth-child(4) { animation-delay: 0.4s; }
.attachment-card:nth-child(5) { animation-delay: 0.5s; }

.viewer-image {
    animation: zoomIn 0.3s ease;
}

/* ===== تحسينات إضافية ===== */
.attachment-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 49%, rgba(255,255,255,0.1) 50%, transparent 51%);
    opacity: 0;
    transition: var(--transition-fast);
}

.attachment-preview:hover::before {
    opacity: 1;
    animation: shine 0.6s ease;
}

@keyframes shine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* ===== تحسينات الوصولية للمرفقات ===== */
.attachment-card:focus-within {
    outline: 2px solid var(--military-gold);
    outline-offset: 2px;
}

.viewer-toolbar .btn:focus {
    outline: 2px solid var(--military-gold);
    outline-offset: 2px;
}

/* ===== تحسينات الطباعة للمرفقات ===== */
@media print {
    .attachment-actions,
    .viewer-toolbar,
    .attachment-overlay {
        display: none !important;
    }

    .attachment-card {
        break-inside: avoid;
        margin-bottom: 1rem;
    }
}

/* ===== تصميم المسح الضوئي المحدث ===== */
.scanner-device-selection {
    background: #f8fafc;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.scanner-preview {
    background: #ffffff;
    border: 2px dashed #cbd5e0;
    border-radius: 10px;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.preview-area {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.no-preview {
    text-align: center;
    color: #718096;
}

.scanner-ready {
    text-align: center;
    padding: 2rem;
}

.scanner-status {
    margin-top: 1rem;
}

.scanner-status .badge {
    margin: 0 0.25rem;
}

.scan-preview {
    text-align: center;
    padding: 1rem;
}

.preview-placeholder {
    background: #f7fafc;
    padding: 2rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.scanner-controls {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    flex-wrap: wrap;
}

.scanner-controls .btn {
    min-width: 120px;
    font-weight: 600;
}

/* ===== قائمة الصفحات الممسوحة ===== */
.pages-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: #ffffff;
}

.no-pages {
    text-align: center;
    padding: 2rem;
    color: #718096;
}

.scanned-page-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid #f1f5f9;
    transition: var(--transition-fast);
}

.scanned-page-item:hover {
    background: #f8fafc;
}

.scanned-page-item:last-child {
    border-bottom: none;
}

.page-thumbnail {
    position: relative;
    width: 60px;
    height: 80px;
    margin-left: 0.75rem;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid #e2e8f0;
    background: #f8fafc;
}

.page-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.page-number {
    position: absolute;
    top: 2px;
    right: 2px;
    background: var(--military-gold);
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 3px;
    line-height: 1;
}

.page-info {
    flex: 1;
    padding: 0 0.5rem;
}

.page-actions {
    display: flex;
    gap: 0.25rem;
}

/* ===== معلومات المسح ===== */
.scan-info {
    background: #f8fafc;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.info-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 500;
    color: var(--military-dark);
}

.info-value {
    font-weight: 600;
    color: var(--military-gold);
}

/* ===== تحسينات المسح الضوئي للشاشات الصغيرة ===== */
@media (max-width: 768px) {
    .scanner-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .scanner-controls .btn {
        min-width: auto;
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .scanner-device-selection .row {
        flex-direction: column;
    }

    .scanner-device-selection .col-md-4 {
        margin-top: 0.75rem;
    }

    .scanner-preview {
        min-height: 250px;
    }

    .pages-list {
        max-height: 250px;
    }

    .scanned-page-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .page-thumbnail {
        margin: 0;
        width: 80px;
        height: 100px;
    }

    .info-item {
        flex-direction: column;
        text-align: center;
        gap: 0.25rem;
    }
}

/* ===== تأثيرات خاصة للمسح الضوئي ===== */
.scanner-ready {
    animation: fadeInUp 0.5s ease;
}

.scanned-page-item {
    animation: slideInRight 0.3s ease;
}

.scan-preview {
    animation: zoomIn 0.3s ease;
}

/* ===== حالات التحميل للمسح ===== */
.scanning-progress {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 10;
}

.scanning-progress .spinner-border {
    width: 3rem;
    height: 3rem;
    color: var(--military-gold);
}

.scanning-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 5;
}

/* ===== تحسينات إضافية للمسح ===== */
.scanner-device-selection h6 {
    color: var(--military-dark);
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.scanner-device-selection select {
    border: 1px solid #cbd5e0;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
}

.scanner-device-selection select:focus {
    border-color: var(--military-gold);
    box-shadow: 0 0 0 0.2rem rgba(214, 158, 46, 0.25);
}

.scanner-controls .btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    border-radius: 8px;
}

.scanner-controls .btn-primary {
    background: linear-gradient(135deg, var(--military-blue), #2c5aa0);
    border: none;
    box-shadow: 0 4px 6px rgba(26, 54, 93, 0.3);
}

.scanner-controls .btn-primary:hover {
    background: linear-gradient(135deg, #1e3a5f, var(--military-blue));
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(26, 54, 93, 0.4);
}

.scanner-controls .btn-success {
    background: linear-gradient(135deg, #38a169, #48bb78);
    border: none;
}

.scanner-controls .btn-warning {
    background: linear-gradient(135deg, #d69e2e, #ecc94b);
    border: none;
    color: var(--military-dark);
}

.scanner-controls .btn-info {
    background: linear-gradient(135deg, #3182ce, #4299e1);
    border: none;
}
