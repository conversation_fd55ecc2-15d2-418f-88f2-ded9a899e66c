/**
 * نظام الإشعارات والتنبيهات للأرشفة العسكرية
 * يدير عرض الإشعارات المختلفة للمستخدم
 */

class NotificationManager {
    constructor() {
        this.container = document.getElementById('notificationContainer');
        this.notifications = [];
        this.defaultDuration = 5000; // 5 ثوانٍ
        this.maxNotifications = 5;
        
        this.initializeContainer();
    }

    /**
     * تهيئة حاوي الإشعارات
     */
    initializeContainer() {
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'notificationContainer';
            this.container.className = 'notification-container';
            document.body.appendChild(this.container);
        }
    }

    /**
     * إظهار إشعار نجاح
     */
    showSuccess(message, title = 'نجح العملية', duration = this.defaultDuration) {
        return this.show({
            type: 'success',
            title: title,
            message: message,
            duration: duration,
            icon: 'fas fa-check-circle'
        });
    }

    /**
     * إظهار إشعار خطأ
     */
    showError(message, title = 'خطأ', duration = this.defaultDuration * 2) {
        return this.show({
            type: 'error',
            title: title,
            message: message,
            duration: duration,
            icon: 'fas fa-exclamation-circle'
        });
    }

    /**
     * إظهار إشعار تحذير
     */
    showWarning(message, title = 'تحذير', duration = this.defaultDuration) {
        return this.show({
            type: 'warning',
            title: title,
            message: message,
            duration: duration,
            icon: 'fas fa-exclamation-triangle'
        });
    }

    /**
     * إظهار إشعار معلومات
     */
    showInfo(message, title = 'معلومات', duration = this.defaultDuration) {
        return this.show({
            type: 'info',
            title: title,
            message: message,
            duration: duration,
            icon: 'fas fa-info-circle'
        });
    }

    /**
     * إظهار إشعار عسكري خاص
     */
    showMilitary(message, title = 'إشعار عسكري', duration = this.defaultDuration) {
        return this.show({
            type: 'military',
            title: title,
            message: message,
            duration: duration,
            icon: 'fas fa-shield-alt'
        });
    }

    /**
     * إظهار إشعار عام
     */
    show(options) {
        // التحقق من الحد الأقصى للإشعارات
        if (this.notifications.length >= this.maxNotifications) {
            this.removeOldest();
        }

        const notification = this.createNotification(options);
        this.notifications.push(notification);
        this.container.appendChild(notification.element);

        // تطبيق الحركة
        setTimeout(() => {
            notification.element.classList.add('show');
        }, 10);

        // إزالة تلقائية
        if (options.duration > 0) {
            notification.timeout = setTimeout(() => {
                this.remove(notification.id);
            }, options.duration);
        }

        return notification.id;
    }

    /**
     * إنشاء عنصر الإشعار
     */
    createNotification(options) {
        const id = this.generateId();
        const element = document.createElement('div');
        
        element.className = `notification notification-${options.type}`;
        element.setAttribute('data-id', id);
        
        element.innerHTML = `
            <div class="notification-content">
                <div class="notification-header">
                    <div class="notification-icon">
                        <i class="${options.icon}"></i>
                    </div>
                    <div class="notification-title">${options.title}</div>
                    <button class="notification-close" onclick="notificationManager.remove('${id}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="notification-message">${options.message}</div>
                ${options.duration > 0 ? `<div class="notification-progress"></div>` : ''}
            </div>
        `;

        // إضافة شريط التقدم إذا كان هناك مدة محددة
        if (options.duration > 0) {
            const progressBar = element.querySelector('.notification-progress');
            progressBar.style.animationDuration = `${options.duration}ms`;
        }

        return {
            id: id,
            element: element,
            options: options,
            timeout: null
        };
    }

    /**
     * إزالة إشعار
     */
    remove(id) {
        const notification = this.notifications.find(n => n.id === id);
        if (!notification) return;

        // إلغاء المؤقت
        if (notification.timeout) {
            clearTimeout(notification.timeout);
        }

        // تطبيق حركة الإزالة
        notification.element.classList.add('removing');
        
        setTimeout(() => {
            if (notification.element.parentNode) {
                notification.element.parentNode.removeChild(notification.element);
            }
            this.notifications = this.notifications.filter(n => n.id !== id);
        }, 300);
    }

    /**
     * إزالة أقدم إشعار
     */
    removeOldest() {
        if (this.notifications.length > 0) {
            this.remove(this.notifications[0].id);
        }
    }

    /**
     * إزالة جميع الإشعارات
     */
    removeAll() {
        this.notifications.forEach(notification => {
            this.remove(notification.id);
        });
    }

    /**
     * إظهار بانر تنبيه في أعلى الصفحة
     */
    showBanner(message, type = 'info', duration = 10000) {
        // إزالة أي بانر موجود
        this.removeBanner();

        const banner = document.createElement('div');
        banner.id = 'alertBanner';
        banner.className = `alert-banner alert-banner-${type}`;
        
        banner.innerHTML = `
            <div class="container-fluid">
                <div class="alert-banner-content">
                    <div class="alert-banner-icon">
                        <i class="${this.getBannerIcon(type)}"></i>
                    </div>
                    <div class="alert-banner-message">${message}</div>
                    <button class="alert-banner-close" onclick="notificationManager.removeBanner()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;

        // إدراج البانر بعد الهيدر
        const header = document.querySelector('.military-header');
        if (header) {
            header.insertAdjacentElement('afterend', banner);
        } else {
            document.body.insertBefore(banner, document.body.firstChild);
        }

        // تطبيق الحركة
        setTimeout(() => {
            banner.classList.add('show');
        }, 10);

        // إزالة تلقائية
        if (duration > 0) {
            setTimeout(() => {
                this.removeBanner();
            }, duration);
        }
    }

    /**
     * إزالة البانر
     */
    removeBanner() {
        const banner = document.getElementById('alertBanner');
        if (banner) {
            banner.classList.add('removing');
            setTimeout(() => {
                if (banner.parentNode) {
                    banner.parentNode.removeChild(banner);
                }
            }, 300);
        }
    }

    /**
     * الحصول على أيقونة البانر حسب النوع
     */
    getBannerIcon(type) {
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle',
            military: 'fas fa-shield-alt'
        };
        return icons[type] || icons.info;
    }

    /**
     * إظهار نافذة تأكيد
     */
    showConfirm(message, title = 'تأكيد العملية', onConfirm = null, onCancel = null) {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.id = 'confirmModal';
            modal.setAttribute('tabindex', '-1');
            
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-question-circle text-warning"></i>
                                ${title}
                            </h5>
                        </div>
                        <div class="modal-body">
                            <p class="mb-0">${message}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                            <button type="button" class="btn btn-primary" id="confirmBtn">
                                <i class="fas fa-check"></i> تأكيد
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            const bootstrapModal = new bootstrap.Modal(modal);
            
            // معالج التأكيد
            modal.querySelector('#confirmBtn').addEventListener('click', () => {
                bootstrapModal.hide();
                if (onConfirm) onConfirm();
                resolve(true);
            });

            // معالج الإلغاء
            modal.addEventListener('hidden.bs.modal', () => {
                if (onCancel) onCancel();
                document.body.removeChild(modal);
                resolve(false);
            });

            bootstrapModal.show();
        });
    }

    /**
     * إظهار نافذة إدخال
     */
    showPrompt(message, title = 'إدخال البيانات', defaultValue = '', onSubmit = null, onCancel = null) {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.id = 'promptModal';
            modal.setAttribute('tabindex', '-1');
            
            modal.innerHTML = `
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-edit text-primary"></i>
                                ${title}
                            </h5>
                        </div>
                        <div class="modal-body">
                            <p class="mb-3">${message}</p>
                            <input type="text" class="form-control" id="promptInput" value="${defaultValue}" placeholder="أدخل القيمة...">
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                            <button type="button" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-check"></i> موافق
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            const bootstrapModal = new bootstrap.Modal(modal);
            const input = modal.querySelector('#promptInput');
            
            // التركيز على الحقل
            modal.addEventListener('shown.bs.modal', () => {
                input.focus();
                input.select();
            });

            // معالج الإرسال
            const submitHandler = () => {
                const value = input.value.trim();
                bootstrapModal.hide();
                if (onSubmit) onSubmit(value);
                resolve(value);
            };

            modal.querySelector('#submitBtn').addEventListener('click', submitHandler);
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    submitHandler();
                }
            });

            // معالج الإلغاء
            modal.addEventListener('hidden.bs.modal', () => {
                if (onCancel) onCancel();
                document.body.removeChild(modal);
                resolve(null);
            });

            bootstrapModal.show();
        });
    }

    /**
     * توليد معرف فريد
     */
    generateId() {
        return 'notification_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
}

// إنشاء مثيل عام لإدارة الإشعارات
window.notificationManager = new NotificationManager();
