/**
 * نظام النسخ الاحتياطي المتقدم
 * يدير النسخ الاحتياطي التلقائي واستيراد/تصدير البيانات
 */

class BackupManager {
    constructor() {
        this.autoBackupEnabled = localStorage.getItem('autoBackupEnabled') === 'true';
        this.backupInterval = parseInt(localStorage.getItem('backupInterval')) || 24; // ساعات
        this.maxBackups = parseInt(localStorage.getItem('maxBackups')) || 10;
        this.lastBackupTime = localStorage.getItem('lastBackupTime');
        
        this.initializeAutoBackup();
    }

    /**
     * تهيئة النسخ الاحتياطي التلقائي
     */
    initializeAutoBackup() {
        if (this.autoBackupEnabled) {
            this.scheduleNextBackup();
        }
    }

    /**
     * جدولة النسخة الاحتياطية التالية
     */
    scheduleNextBackup() {
        const now = new Date();
        const lastBackup = this.lastBackupTime ? new Date(this.lastBackupTime) : new Date(0);
        const nextBackup = new Date(lastBackup.getTime() + (this.backupInterval * 60 * 60 * 1000));
        
        if (now >= nextBackup) {
            this.createAutoBackup();
        } else {
            const timeUntilBackup = nextBackup.getTime() - now.getTime();
            setTimeout(() => {
                this.createAutoBackup();
            }, timeUntilBackup);
        }
    }

    /**
     * إنشاء نسخة احتياطية تلقائية
     */
    createAutoBackup() {
        try {
            const backupData = this.createBackupData();
            this.saveBackupToStorage(backupData, 'auto');
            
            localStorage.setItem('lastBackupTime', new Date().toISOString());
            
            // جدولة النسخة التالية
            setTimeout(() => {
                this.createAutoBackup();
            }, this.backupInterval * 60 * 60 * 1000);
            
            console.log('تم إنشاء نسخة احتياطية تلقائية');
            
        } catch (error) {
            console.error('خطأ في النسخ الاحتياطي التلقائي:', error);
        }
    }

    /**
     * إنشاء بيانات النسخة الاحتياطية
     */
    createBackupData() {
        return {
            version: '1.0',
            timestamp: new Date().toISOString(),
            data: dataManager.data,
            settings: {
                colorTheme: localStorage.getItem('colorTheme'),
                fontSize: localStorage.getItem('fontSize'),
                enableAnimations: localStorage.getItem('enableAnimations'),
                autoBackupEnabled: this.autoBackupEnabled,
                backupInterval: this.backupInterval,
                maxBackups: this.maxBackups
            },
            metadata: {
                totalStudents: dataManager.data.students.length,
                totalCourses: dataManager.data.courses.length,
                totalRecords: dataManager.data.dailyRecords.length,
                totalUsers: dataManager.data.users.length
            }
        };
    }

    /**
     * حفظ النسخة الاحتياطية في التخزين المحلي
     */
    saveBackupToStorage(backupData, type = 'manual') {
        const backupKey = `backup_${type}_${Date.now()}`;
        const compressedData = this.compressData(JSON.stringify(backupData));
        
        try {
            localStorage.setItem(backupKey, compressedData);
            this.cleanupOldBackups(type);
            return backupKey;
        } catch (error) {
            if (error.name === 'QuotaExceededError') {
                this.cleanupOldBackups(type, true);
                localStorage.setItem(backupKey, compressedData);
                return backupKey;
            }
            throw error;
        }
    }

    /**
     * تنظيف النسخ الاحتياطية القديمة
     */
    cleanupOldBackups(type, force = false) {
        const backups = this.getStoredBackups(type);
        const maxAllowed = force ? Math.floor(this.maxBackups / 2) : this.maxBackups;
        
        if (backups.length >= maxAllowed) {
            // ترتيب حسب التاريخ (الأقدم أولاً)
            backups.sort((a, b) => a.timestamp - b.timestamp);
            
            // حذف النسخ الزائدة
            const toDelete = backups.slice(0, backups.length - maxAllowed + 1);
            toDelete.forEach(backup => {
                localStorage.removeItem(backup.key);
            });
        }
    }

    /**
     * الحصول على النسخ الاحتياطية المحفوظة
     */
    getStoredBackups(type = null) {
        const backups = [];
        const prefix = type ? `backup_${type}_` : 'backup_';
        
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(prefix)) {
                const timestamp = parseInt(key.split('_').pop());
                backups.push({
                    key: key,
                    timestamp: timestamp,
                    date: new Date(timestamp),
                    type: key.includes('_auto_') ? 'auto' : 'manual',
                    size: localStorage.getItem(key).length
                });
            }
        }
        
        return backups.sort((a, b) => b.timestamp - a.timestamp);
    }

    /**
     * تصدير نسخة احتياطية
     */
    exportBackup(includeAttachments = false) {
        try {
            const backupData = this.createBackupData();
            
            if (includeAttachments) {
                backupData.attachments = this.exportAttachments();
            }
            
            const blob = new Blob([JSON.stringify(backupData, null, 2)], { 
                type: 'application/json' 
            });
            
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `military_archive_backup_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            notificationManager.showSuccess('تم تصدير النسخة الاحتياطية بنجاح');
            
        } catch (error) {
            notificationManager.showError('حدث خطأ أثناء التصدير: ' + error.message);
        }
    }

    /**
     * استيراد نسخة احتياطية
     */
    importBackup(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const backupData = JSON.parse(e.target.result);
                    
                    // التحقق من صحة البيانات
                    if (!this.validateBackupData(backupData)) {
                        throw new Error('ملف النسخة الاحتياطية غير صالح');
                    }
                    
                    // عرض معلومات النسخة الاحتياطية
                    this.showBackupInfo(backupData, (confirmed) => {
                        if (confirmed) {
                            this.restoreFromBackup(backupData);
                            resolve(true);
                        } else {
                            resolve(false);
                        }
                    });
                    
                } catch (error) {
                    reject(error);
                }
            };
            
            reader.onerror = () => {
                reject(new Error('فشل في قراءة الملف'));
            };
            
            reader.readAsText(file);
        });
    }

    /**
     * التحقق من صحة بيانات النسخة الاحتياطية
     */
    validateBackupData(backupData) {
        return (
            backupData &&
            backupData.version &&
            backupData.timestamp &&
            backupData.data &&
            backupData.data.students &&
            backupData.data.courses &&
            backupData.data.dailyRecords &&
            backupData.data.users &&
            backupData.data.settings
        );
    }

    /**
     * عرض معلومات النسخة الاحتياطية
     */
    showBackupInfo(backupData, callback) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-info-circle"></i> معلومات النسخة الاحتياطية
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="backup-info">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>معلومات عامة</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>الإصدار:</strong> ${backupData.version}</li>
                                        <li><strong>التاريخ:</strong> ${new Date(backupData.timestamp).toLocaleString('ar-SA')}</li>
                                        <li><strong>الحجم:</strong> ${this.formatFileSize(JSON.stringify(backupData).length)}</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>محتويات النسخة</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>الطلاب:</strong> ${backupData.metadata?.totalStudents || backupData.data.students.length}</li>
                                        <li><strong>الدورات:</strong> ${backupData.metadata?.totalCourses || backupData.data.courses.length}</li>
                                        <li><strong>اليوميات:</strong> ${backupData.metadata?.totalRecords || backupData.data.dailyRecords.length}</li>
                                        <li><strong>المستخدمين:</strong> ${backupData.metadata?.totalUsers || backupData.data.users.length}</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="alert alert-warning mt-3">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>تحذير:</strong> سيتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية.
                                يُنصح بإنشاء نسخة احتياطية من البيانات الحالية قبل المتابعة.
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                        <button type="button" class="btn btn-warning" id="createCurrentBackup">
                            <i class="fas fa-shield-alt"></i> إنشاء نسخة احتياطية أولاً
                        </button>
                        <button type="button" class="btn btn-primary" id="confirmRestore">
                            <i class="fas fa-upload"></i> استعادة البيانات
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);
        
        // إنشاء نسخة احتياطية من البيانات الحالية
        modal.querySelector('#createCurrentBackup').addEventListener('click', () => {
            this.exportBackup();
        });
        
        // تأكيد الاستعادة
        modal.querySelector('#confirmRestore').addEventListener('click', () => {
            bootstrapModal.hide();
            callback(true);
        });
        
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
            callback(false);
        });

        bootstrapModal.show();
    }

    /**
     * استعادة البيانات من النسخة الاحتياطية
     */
    restoreFromBackup(backupData) {
        try {
            // حفظ نسخة احتياطية من البيانات الحالية
            const currentBackup = this.createBackupData();
            this.saveBackupToStorage(currentBackup, 'pre_restore');
            
            // استعادة البيانات
            dataManager.data = backupData.data;
            dataManager.saveData();
            
            // استعادة الإعدادات
            if (backupData.settings) {
                Object.entries(backupData.settings).forEach(([key, value]) => {
                    if (value !== null && value !== undefined) {
                        localStorage.setItem(key, value);
                    }
                });
            }
            
            // استعادة المرفقات إذا كانت موجودة
            if (backupData.attachments) {
                this.restoreAttachments(backupData.attachments);
            }
            
            notificationManager.showSuccess('تم استعادة البيانات بنجاح. سيتم إعادة تحميل الصفحة.');
            
            // إعادة تحميل الصفحة لتطبيق التغييرات
            setTimeout(() => {
                location.reload();
            }, 2000);
            
        } catch (error) {
            notificationManager.showError('حدث خطأ أثناء استعادة البيانات: ' + error.message);
        }
    }

    /**
     * فتح نافذة إدارة النسخ الاحتياطية
     */
    openBackupManager() {
        const modal = document.createElement('div');
        modal.className = 'modal fade backup-manager-modal';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-database"></i> إدارة النسخ الاحتياطية
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="backup-actions">
                                    <h6><i class="fas fa-tools"></i> الإجراءات</h6>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-primary" id="createManualBackup">
                                            <i class="fas fa-plus"></i> إنشاء نسخة احتياطية
                                        </button>
                                        <button class="btn btn-success" id="exportBackupBtn">
                                            <i class="fas fa-download"></i> تصدير نسخة احتياطية
                                        </button>
                                        <button class="btn btn-warning" id="importBackupBtn">
                                            <i class="fas fa-upload"></i> استيراد نسخة احتياطية
                                        </button>
                                        <button class="btn btn-info" id="autoBackupSettings">
                                            <i class="fas fa-cog"></i> إعدادات النسخ التلقائي
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="backup-stats mt-4">
                                    <h6><i class="fas fa-chart-bar"></i> الإحصائيات</h6>
                                    <div class="stats-list">
                                        <div class="stat-item">
                                            <span>النسخ المحفوظة:</span>
                                            <span id="totalBackups">0</span>
                                        </div>
                                        <div class="stat-item">
                                            <span>آخر نسخة تلقائية:</span>
                                            <span id="lastAutoBackup">-</span>
                                        </div>
                                        <div class="stat-item">
                                            <span>المساحة المستخدمة:</span>
                                            <span id="usedSpace">0 KB</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="backup-list">
                                    <h6><i class="fas fa-list"></i> النسخ الاحتياطية المحفوظة</h6>
                                    <div id="backupsList">
                                        <!-- سيتم تحميل القائمة هنا -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);
        
        this.setupBackupManagerEvents(modal);
        this.loadBackupsList(modal);
        this.updateBackupStats(modal);
        
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });

        bootstrapModal.show();
    }

    /**
     * إعداد أحداث مدير النسخ الاحتياطية
     */
    setupBackupManagerEvents(modal) {
        // إنشاء نسخة احتياطية يدوية
        modal.querySelector('#createManualBackup').addEventListener('click', () => {
            const backupData = this.createBackupData();
            const backupKey = this.saveBackupToStorage(backupData, 'manual');
            notificationManager.showSuccess('تم إنشاء النسخة الاحتياطية بنجاح');
            this.loadBackupsList(modal);
            this.updateBackupStats(modal);
        });

        // تصدير نسخة احتياطية
        modal.querySelector('#exportBackupBtn').addEventListener('click', () => {
            this.exportBackup();
        });

        // استيراد نسخة احتياطية
        modal.querySelector('#importBackupBtn').addEventListener('click', () => {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = (e) => {
                const file = e.target.files[0];
                if (file) {
                    this.importBackup(file).then(() => {
                        this.loadBackupsList(modal);
                        this.updateBackupStats(modal);
                    }).catch(error => {
                        notificationManager.showError('فشل في استيراد النسخة الاحتياطية: ' + error.message);
                    });
                }
            };
            input.click();
        });

        // إعدادات النسخ التلقائي
        modal.querySelector('#autoBackupSettings').addEventListener('click', () => {
            this.showAutoBackupSettings();
        });
    }

    /**
     * تحميل قائمة النسخ الاحتياطية
     */
    loadBackupsList(modal) {
        const container = modal.querySelector('#backupsList');
        const backups = this.getStoredBackups();
        
        if (backups.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-database fa-2x mb-2"></i>
                    <p>لا توجد نسخ احتياطية محفوظة</p>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>النوع</th>
                            <th>التاريخ</th>
                            <th>الحجم</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${backups.map(backup => `
                            <tr>
                                <td>
                                    <span class="badge bg-${backup.type === 'auto' ? 'info' : 'primary'}">
                                        ${backup.type === 'auto' ? 'تلقائي' : 'يدوي'}
                                    </span>
                                </td>
                                <td>${backup.date.toLocaleString('ar-SA')}</td>
                                <td>${this.formatFileSize(backup.size)}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-success" onclick="backupManager.restoreBackup('${backup.key}')">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary" onclick="backupManager.downloadBackup('${backup.key}')">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="backupManager.deleteBackup('${backup.key}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * تحديث إحصائيات النسخ الاحتياطية
     */
    updateBackupStats(modal) {
        const backups = this.getStoredBackups();
        const totalSize = backups.reduce((sum, backup) => sum + backup.size, 0);
        const lastAuto = backups.find(b => b.type === 'auto');
        
        modal.querySelector('#totalBackups').textContent = backups.length;
        modal.querySelector('#lastAutoBackup').textContent = 
            lastAuto ? lastAuto.date.toLocaleString('ar-SA') : 'لا يوجد';
        modal.querySelector('#usedSpace').textContent = this.formatFileSize(totalSize);
    }

    // الوظائف المساعدة
    compressData(data) {
        // في التطبيق الحقيقي، يمكن استخدام مكتبة ضغط
        return data;
    }

    decompressData(data) {
        // في التطبيق الحقيقي، يمكن استخدام مكتبة إلغاء الضغط
        return data;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    exportAttachments() {
        // تصدير المرفقات - سيتم تطويره لاحقاً
        return [];
    }

    restoreAttachments(attachments) {
        // استعادة المرفقات - سيتم تطويره لاحقاً
    }
}

// إنشاء مثيل عام لإدارة النسخ الاحتياطية
window.backupManager = new BackupManager();
