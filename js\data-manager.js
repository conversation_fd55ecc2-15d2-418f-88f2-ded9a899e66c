/**
 * نظام إدارة البيانات للأرشفة العسكرية
 * يدير البيانات المحلية والبحث التلقائي
 */

class DataManager {
    constructor() {
        this.storageKey = 'military_archive_data';
        this.data = this.loadData();
        this.initializeDefaultData();
    }

    /**
     * تحميل البيانات من التخزين المحلي
     */
    loadData() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            return stored ? JSON.parse(stored) : this.getDefaultData();
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
            return this.getDefaultData();
        }
    }

    /**
     * حفظ البيانات في التخزين المحلي
     */
    saveData() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.data));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
            return false;
        }
    }

    /**
     * الحصول على البيانات الافتراضية
     */
    getDefaultData() {
        return {
            students: [],
            courses: [],
            dailyRecords: [],
            users: [
                {
                    id: 1,
                    name: 'المشرف العام',
                    username: 'admin',
                    role: 'admin',
                    permissions: ['all']
                }
            ],
            settings: {
                maxDelayHours: 7,
                maxAbsenceHours: 7,
                violationTypes: [
                    'عدم ارتداء الزي الرسمي',
                    'عدم الانضباط في الطابور',
                    'التأخير عن المحاضرة',
                    'عدم أداء التحية العسكرية',
                    'استخدام الهاتف أثناء المحاضرة',
                    'عدم إحضار الأدوات المطلوبة',
                    'السلوك غير اللائق',
                    'مخالفة أخرى'
                ],
                absenceTypes: [
                    'غياب بدون عذر',
                    'غياب بعذر مرضي',
                    'غياب بعذر طارئ',
                    'إجازة رسمية'
                ]
            }
        };
    }

    /**
     * تهيئة البيانات الافتراضية إذا لم تكن موجودة
     */
    initializeDefaultData() {
        if (!this.data.students) this.data.students = [];
        if (!this.data.courses) this.data.courses = [];
        if (!this.data.dailyRecords) this.data.dailyRecords = [];
        if (!this.data.users) this.data.users = this.getDefaultData().users;
        if (!this.data.settings) this.data.settings = this.getDefaultData().settings;
    }

    // ===== إدارة الطلاب =====

    /**
     * البحث عن طالب بالاسم أو الرقم العسكري
     */
    searchStudent(query) {
        if (!query || query.trim() === '') return [];
        
        const searchTerm = query.toLowerCase().trim();
        return this.data.students.filter(student => 
            student.name.toLowerCase().includes(searchTerm) ||
            student.militaryNumber.toLowerCase().includes(searchTerm)
        );
    }

    /**
     * الحصول على طالب بالرقم العسكري
     */
    getStudentByMilitaryNumber(militaryNumber) {
        return this.data.students.find(student => 
            student.militaryNumber === militaryNumber
        );
    }

    /**
     * إضافة طالب جديد
     */
    addStudent(studentData) {
        // التحقق من عدم وجود الرقم العسكري مسبقاً
        if (this.getStudentByMilitaryNumber(studentData.militaryNumber)) {
            throw new Error('الرقم العسكري موجود مسبقاً');
        }

        const student = {
            id: this.generateId(),
            name: studentData.name,
            militaryNumber: studentData.militaryNumber,
            rank: studentData.rank,
            courseCode: studentData.courseCode,
            courseNumber: studentData.courseNumber,
            courseName: studentData.courseName,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        this.data.students.push(student);
        this.saveData();
        return student;
    }

    /**
     * تحديث بيانات طالب
     */
    updateStudent(id, updates) {
        const index = this.data.students.findIndex(student => student.id === id);
        if (index === -1) {
            throw new Error('الطالب غير موجود');
        }

        this.data.students[index] = {
            ...this.data.students[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };

        this.saveData();
        return this.data.students[index];
    }

    // ===== إدارة الدورات =====

    /**
     * البحث عن دورة برمز الدورة ورقم الدورة
     */
    searchCourse(courseCode, courseNumber) {
        return this.data.courses.find(course => 
            course.courseCode === courseCode && course.courseNumber === courseNumber
        );
    }

    /**
     * إضافة دورة جديدة
     */
    addCourse(courseData) {
        // التحقق من عدم وجود الدورة مسبقاً
        if (this.searchCourse(courseData.courseCode, courseData.courseNumber)) {
            throw new Error('الدورة موجودة مسبقاً');
        }

        const course = {
            id: this.generateId(),
            courseCode: courseData.courseCode,
            courseNumber: courseData.courseNumber,
            courseName: courseData.courseName,
            startDate: courseData.startDate,
            endDate: courseData.endDate,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        this.data.courses.push(course);
        this.saveData();
        return course;
    }

    /**
     * الحصول على جميع الدورات
     */
    getAllCourses() {
        return this.data.courses;
    }

    // ===== إدارة اليوميات =====

    /**
     * إضافة يومية جديدة
     */
    addDailyRecord(recordData) {
        const record = {
            id: this.generateId(),
            date: recordData.date,
            supervisor: recordData.supervisor,
            students: recordData.students,
            attachments: recordData.attachments || [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        this.data.dailyRecords.push(record);
        this.saveData();
        return record;
    }

    /**
     * الحصول على اليوميات بالتاريخ
     */
    getDailyRecordsByDate(date) {
        return this.data.dailyRecords.filter(record => record.date === date);
    }

    /**
     * الحصول على جميع اليوميات
     */
    getAllDailyRecords() {
        return this.data.dailyRecords.sort((a, b) => 
            new Date(b.date) - new Date(a.date)
        );
    }

    /**
     * البحث في اليوميات
     */
    searchDailyRecords(filters) {
        let records = this.data.dailyRecords;

        if (filters.date) {
            records = records.filter(record => record.date === filters.date);
        }

        if (filters.supervisor) {
            records = records.filter(record => 
                record.supervisor.toLowerCase().includes(filters.supervisor.toLowerCase())
            );
        }

        if (filters.studentName) {
            records = records.filter(record => 
                record.students.some(student => 
                    student.name.toLowerCase().includes(filters.studentName.toLowerCase())
                )
            );
        }

        return records.sort((a, b) => new Date(b.date) - new Date(a.date));
    }

    // ===== الإحصائيات =====

    /**
     * حساب إجمالي المخالفات
     */
    getTotalViolations() {
        return this.data.dailyRecords.reduce((total, record) => {
            return total + record.students.reduce((studentTotal, student) => {
                return studentTotal + (student.violations ? student.violations.length : 0);
            }, 0);
        }, 0);
    }

    /**
     * حساب إجمالي التأخيرات
     */
    getTotalDelays() {
        return this.data.dailyRecords.reduce((total, record) => {
            return total + record.students.reduce((studentTotal, student) => {
                return studentTotal + (student.delays || 0);
            }, 0);
        }, 0);
    }

    /**
     * حساب إجمالي الغيابات
     */
    getTotalAbsences() {
        return this.data.dailyRecords.reduce((total, record) => {
            return total + record.students.reduce((studentTotal, student) => {
                return studentTotal + (student.absences || 0);
            }, 0);
        }, 0);
    }

    /**
     * حساب إجمالي الطلاب
     */
    getTotalStudents() {
        return this.data.students.length;
    }

    // ===== الوظائف المساعدة =====

    /**
     * توليد معرف فريد
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * تنسيق التاريخ
     */
    formatDate(date) {
        return new Date(date).toLocaleDateString('ar-SA');
    }

    /**
     * التحقق من صحة البيانات
     */
    validateStudentData(data) {
        const errors = [];

        if (!data.name || data.name.trim() === '') {
            errors.push('اسم الطالب مطلوب');
        }

        if (!data.militaryNumber || data.militaryNumber.trim() === '') {
            errors.push('الرقم العسكري مطلوب');
        }

        if (!data.rank || data.rank.trim() === '') {
            errors.push('الرتبة مطلوبة');
        }

        if (!data.courseCode || data.courseCode.trim() === '') {
            errors.push('رمز الدورة مطلوب');
        }

        if (!data.courseNumber || data.courseNumber.trim() === '') {
            errors.push('رقم الدورة مطلوب');
        }

        return errors;
    }

    /**
     * تصدير البيانات
     */
    exportData() {
        return JSON.stringify(this.data, null, 2);
    }

    /**
     * استيراد البيانات
     */
    importData(jsonData) {
        try {
            const importedData = JSON.parse(jsonData);
            this.data = { ...this.getDefaultData(), ...importedData };
            this.saveData();
            return true;
        } catch (error) {
            console.error('خطأ في استيراد البيانات:', error);
            return false;
        }
    }

    /**
     * مسح جميع البيانات
     */
    clearAllData() {
        this.data = this.getDefaultData();
        this.saveData();
    }
}

// إنشاء مثيل عام لإدارة البيانات
window.dataManager = new DataManager();
