/* ===== دعم اللغة العربية والكتابة من اليمين إلى اليسار ===== */

/* ===== الإعدادات الأساسية للـ RTL ===== */
html[dir="rtl"] {
    direction: rtl;
    text-align: right;
}

html[dir="rtl"] body {
    font-family: 'Cairo', 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    text-align: right;
}

/* ===== تعديل الهيدر للـ RTL ===== */
html[dir="rtl"] .military-header .row {
    direction: rtl;
}

html[dir="rtl"] .header-title i {
    margin-right: 10px;
    margin-left: 0;
}

html[dir="rtl"] .user-info {
    text-align: left;
    flex-direction: row-reverse;
}

html[dir="rtl"] .user-info i {
    margin-right: 8px;
    margin-left: 0;
}

/* ===== تعديل القائمة الجانبية للـ RTL ===== */
html[dir="rtl"] .sidebar {
    right: -300px;
    left: auto;
}

html[dir="rtl"] .sidebar.active {
    right: 0;
    left: auto;
}

html[dir="rtl"] .sidebar-header h3 {
    text-align: center;
}

html[dir="rtl"] .menu-item a {
    text-align: right;
    padding: 1rem 1.5rem;
}

html[dir="rtl"] .menu-item a:hover,
html[dir="rtl"] .menu-item.active a {
    transform: translateX(5px);
}

html[dir="rtl"] .menu-item a i {
    margin-right: 10px;
    margin-left: 0;
    float: right;
}

/* ===== تعديل المحتوى الرئيسي للـ RTL ===== */
html[dir="rtl"] .main-content.sidebar-open {
    margin-left: 300px;
    margin-right: 0;
}

/* ===== تعديل البانر الترحيبي للـ RTL ===== */
html[dir="rtl"] .banner-content h2 i {
    margin-right: 15px;
    margin-left: 0;
}

/* ===== تعديل بطاقات الإحصائيات للـ RTL ===== */
html[dir="rtl"] .card-icon {
    right: 1rem;
    left: auto;
}

html[dir="rtl"] .card-content {
    text-align: center;
}

/* ===== تعديل الإجراءات السريعة للـ RTL ===== */
html[dir="rtl"] .quick-actions h3 i {
    margin-right: 10px;
    margin-left: 0;
}

html[dir="rtl"] .action-btn i {
    margin-right: 8px;
    margin-left: 0;
}

/* ===== تعديل رؤوس الصفحات للـ RTL ===== */
html[dir="rtl"] .page-header {
    border-left: 5px solid var(--military-gold);
    border-right: none;
}

html[dir="rtl"] .page-header h2 i {
    margin-right: 15px;
    margin-left: 0;
}

/* ===== تعديل النماذج للـ RTL ===== */
html[dir="rtl"] .form-group label {
    text-align: right;
}

html[dir="rtl"] .form-control {
    text-align: right;
    direction: rtl;
}

html[dir="rtl"] .input-group-text {
    border-radius: 0 0.375rem 0.375rem 0;
}

html[dir="rtl"] .input-group .form-control {
    border-radius: 0.375rem 0 0 0.375rem;
}

/* ===== تعديل الأزرار للـ RTL ===== */
html[dir="rtl"] .btn i {
    margin-right: 5px;
    margin-left: 0;
}

html[dir="rtl"] .btn-group .btn:first-child {
    border-radius: 0 0.375rem 0.375rem 0;
}

html[dir="rtl"] .btn-group .btn:last-child {
    border-radius: 0.375rem 0 0 0.375rem;
}

/* ===== تعديل الجداول للـ RTL ===== */
html[dir="rtl"] .table {
    text-align: right;
}

html[dir="rtl"] .table th,
html[dir="rtl"] .table td {
    text-align: right;
}

html[dir="rtl"] .table-responsive {
    direction: rtl;
}

/* ===== تعديل النوافذ المنبثقة للـ RTL ===== */
html[dir="rtl"] .modal-header {
    text-align: right;
}

html[dir="rtl"] .modal-header .btn-close {
    margin: 0 auto 0 0;
}

html[dir="rtl"] .modal-title {
    text-align: right;
}

html[dir="rtl"] .modal-body {
    text-align: right;
}

html[dir="rtl"] .modal-footer {
    justify-content: flex-start;
}

/* ===== تعديل الإشعارات للـ RTL ===== */
html[dir="rtl"] .notification-container {
    right: 20px;
    left: auto;
}

html[dir="rtl"] .notification {
    text-align: right;
    direction: rtl;
}

html[dir="rtl"] .notification .notification-icon {
    float: right;
    margin-left: 10px;
    margin-right: 0;
}

html[dir="rtl"] .notification .notification-close {
    float: left;
}

/* ===== تعديل القوائم المنسدلة للـ RTL ===== */
html[dir="rtl"] .dropdown-menu {
    text-align: right;
    right: 0;
    left: auto;
}

html[dir="rtl"] .dropdown-item {
    text-align: right;
}

html[dir="rtl"] .dropdown-toggle::after {
    margin-right: 0.255em;
    margin-left: 0;
}

/* ===== تعديل التنقل والصفحات للـ RTL ===== */
html[dir="rtl"] .pagination {
    direction: rtl;
}

html[dir="rtl"] .page-link {
    text-align: center;
}

html[dir="rtl"] .breadcrumb {
    direction: rtl;
}

html[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
    content: "/";
    float: right;
    padding-left: 0;
    padding-right: 0.5rem;
}

/* ===== تعديل البطاقات للـ RTL ===== */
html[dir="rtl"] .card-header {
    text-align: right;
}

html[dir="rtl"] .card-body {
    text-align: right;
}

html[dir="rtl"] .card-footer {
    text-align: right;
}

/* ===== تعديل الشبكة للـ RTL ===== */
html[dir="rtl"] .row {
    direction: rtl;
}

html[dir="rtl"] .col,
html[dir="rtl"] [class*="col-"] {
    text-align: right;
}

/* ===== تعديل الفلكس للـ RTL ===== */
html[dir="rtl"] .d-flex {
    direction: rtl;
}

html[dir="rtl"] .justify-content-start {
    justify-content: flex-end !important;
}

html[dir="rtl"] .justify-content-end {
    justify-content: flex-start !important;
}

html[dir="rtl"] .text-start {
    text-align: right !important;
}

html[dir="rtl"] .text-end {
    text-align: left !important;
}

/* ===== تعديل الهوامش والحشو للـ RTL ===== */
html[dir="rtl"] .me-1 { margin-left: 0.25rem !important; margin-right: 0 !important; }
html[dir="rtl"] .me-2 { margin-left: 0.5rem !important; margin-right: 0 !important; }
html[dir="rtl"] .me-3 { margin-left: 1rem !important; margin-right: 0 !important; }
html[dir="rtl"] .me-4 { margin-left: 1.5rem !important; margin-right: 0 !important; }
html[dir="rtl"] .me-5 { margin-left: 3rem !important; margin-right: 0 !important; }

html[dir="rtl"] .ms-1 { margin-right: 0.25rem !important; margin-left: 0 !important; }
html[dir="rtl"] .ms-2 { margin-right: 0.5rem !important; margin-left: 0 !important; }
html[dir="rtl"] .ms-3 { margin-right: 1rem !important; margin-left: 0 !important; }
html[dir="rtl"] .ms-4 { margin-right: 1.5rem !important; margin-left: 0 !important; }
html[dir="rtl"] .ms-5 { margin-right: 3rem !important; margin-left: 0 !important; }

html[dir="rtl"] .pe-1 { padding-left: 0.25rem !important; padding-right: 0 !important; }
html[dir="rtl"] .pe-2 { padding-left: 0.5rem !important; padding-right: 0 !important; }
html[dir="rtl"] .pe-3 { padding-left: 1rem !important; padding-right: 0 !important; }
html[dir="rtl"] .pe-4 { padding-left: 1.5rem !important; padding-right: 0 !important; }
html[dir="rtl"] .pe-5 { padding-left: 3rem !important; padding-right: 0 !important; }

html[dir="rtl"] .ps-1 { padding-right: 0.25rem !important; padding-left: 0 !important; }
html[dir="rtl"] .ps-2 { padding-right: 0.5rem !important; padding-left: 0 !important; }
html[dir="rtl"] .ps-3 { padding-right: 1rem !important; padding-left: 0 !important; }
html[dir="rtl"] .ps-4 { padding-right: 1.5rem !important; padding-left: 0 !important; }
html[dir="rtl"] .ps-5 { padding-right: 3rem !important; padding-left: 0 !important; }

/* ===== تعديل الحركات للـ RTL ===== */
html[dir="rtl"] .sidebar {
    animation: slideInLeft 0.3s ease;
}

html[dir="rtl"] .menu-item {
    animation: fadeInLeft 0.5s ease forwards;
}

html[dir="rtl"] .notification {
    animation: slideInRight 0.5s ease;
}

html[dir="rtl"] .notification.removing {
    animation: slideInLeft 0.3s ease;
}

/* ===== تحسينات خاصة بالخط العربي ===== */
html[dir="rtl"] {
    font-feature-settings: "liga" 1, "kern" 1;
    text-rendering: optimizeLegibility;
}

html[dir="rtl"] h1, 
html[dir="rtl"] h2, 
html[dir="rtl"] h3, 
html[dir="rtl"] h4, 
html[dir="rtl"] h5, 
html[dir="rtl"] h6 {
    font-weight: 600;
    line-height: 1.4;
}

html[dir="rtl"] p {
    line-height: 1.8;
}

/* ===== تعديلات للشاشات الصغيرة ===== */
@media (max-width: 768px) {
    html[dir="rtl"] .sidebar {
        width: 280px;
        right: -280px;
    }
    
    html[dir="rtl"] .main-content.sidebar-open {
        margin-left: 0;
        margin-right: 0;
    }
    
    html[dir="rtl"] .notification-container {
        right: 10px;
        left: auto;
        max-width: calc(100% - 20px);
    }
}
