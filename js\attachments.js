/**
 * نظام إدارة المرفقات المتقدم
 * يدير عرض وتكبير وتصغير المرفقات مع معاينة متقدمة
 */

class AttachmentsManager {
    constructor() {
        this.attachments = [];
        this.currentViewerIndex = 0;
        this.zoomLevel = 1;
        this.maxZoom = 5;
        this.minZoom = 0.1;
    }

    /**
     * إضافة مرفق جديد
     */
    addAttachment(file, dataURL = null) {
        const attachment = {
            id: this.generateId(),
            name: file.name,
            size: file.size,
            type: file.type,
            file: file,
            dataURL: dataURL,
            uploadDate: new Date().toISOString(),
            thumbnail: null
        };

        // إنشاء صورة مصغرة للملفات المدعومة
        if (this.isImageFile(file)) {
            this.createThumbnail(file, attachment);
        } else if (this.isPDFFile(file)) {
            this.createPDFThumbnail(file, attachment);
        }

        this.attachments.push(attachment);
        this.updateAttachmentsDisplay();
        
        return attachment;
    }

    /**
     * إنشاء صورة مصغرة
     */
    createThumbnail(file, attachment) {
        const reader = new FileReader();
        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                // حساب الأبعاد المناسبة للصورة المصغرة
                const maxSize = 150;
                let { width, height } = img;
                
                if (width > height) {
                    if (width > maxSize) {
                        height = (height * maxSize) / width;
                        width = maxSize;
                    }
                } else {
                    if (height > maxSize) {
                        width = (width * maxSize) / height;
                        height = maxSize;
                    }
                }
                
                canvas.width = width;
                canvas.height = height;
                ctx.drawImage(img, 0, 0, width, height);
                
                attachment.thumbnail = canvas.toDataURL('image/jpeg', 0.8);
                this.updateAttachmentsDisplay();
            };
            img.src = e.target.result;
            attachment.dataURL = e.target.result;
        };
        reader.readAsDataURL(file);
    }

    /**
     * إنشاء صورة مصغرة لملف PDF
     */
    createPDFThumbnail(file, attachment) {
        // في التطبيق الحقيقي، يمكن استخدام مكتبة PDF.js
        attachment.thumbnail = this.getFileTypeIcon(file.type);
    }

    /**
     * الحصول على أيقونة نوع الملف
     */
    getFileTypeIcon(mimeType) {
        const iconMap = {
            'application/pdf': 'fas fa-file-pdf',
            'application/msword': 'fas fa-file-word',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'fas fa-file-word',
            'application/vnd.ms-excel': 'fas fa-file-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'fas fa-file-excel',
            'text/plain': 'fas fa-file-alt',
            'image/jpeg': 'fas fa-file-image',
            'image/png': 'fas fa-file-image',
            'image/gif': 'fas fa-file-image',
            'image/webp': 'fas fa-file-image'
        };
        
        return iconMap[mimeType] || 'fas fa-file';
    }

    /**
     * تحديث عرض المرفقات
     */
    updateAttachmentsDisplay() {
        const container = document.getElementById('attachmentsContainer');
        if (!container) return;

        if (this.attachments.length === 0) {
            container.innerHTML = `
                <div class="no-attachments">
                    <i class="fas fa-paperclip fa-2x text-muted mb-2"></i>
                    <p class="text-muted">لا توجد مرفقات</p>
                    <small class="text-muted">اسحب الملفات هنا أو استخدم الأزرار أعلاه</small>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <div class="attachments-grid">
                ${this.attachments.map((attachment, index) => this.createAttachmentCard(attachment, index)).join('')}
            </div>
        `;
    }

    /**
     * إنشاء بطاقة مرفق
     */
    createAttachmentCard(attachment, index) {
        const isImage = this.isImageFile(attachment.file);
        const isPDF = this.isPDFFile(attachment.file);
        
        return `
            <div class="attachment-card" data-id="${attachment.id}">
                <div class="attachment-preview" onclick="attachmentsManager.openViewer(${index})">
                    ${attachment.thumbnail ? 
                        `<img src="${attachment.thumbnail}" alt="${attachment.name}" class="attachment-thumbnail">` :
                        `<div class="attachment-icon"><i class="${this.getFileTypeIcon(attachment.type)} fa-2x"></i></div>`
                    }
                    <div class="attachment-overlay">
                        <i class="fas fa-eye"></i>
                    </div>
                </div>
                <div class="attachment-info">
                    <div class="attachment-name" title="${attachment.name}">${this.truncateFileName(attachment.name)}</div>
                    <div class="attachment-size">${this.formatFileSize(attachment.size)}</div>
                    <div class="attachment-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="attachmentsManager.downloadAttachment('${attachment.id}')" title="تحميل">
                            <i class="fas fa-download"></i>
                        </button>
                        ${isImage ? `
                            <button class="btn btn-sm btn-outline-info" onclick="attachmentsManager.editImage('${attachment.id}')" title="تحرير">
                                <i class="fas fa-edit"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-sm btn-outline-danger" onclick="attachmentsManager.removeAttachment('${attachment.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * فتح عارض المرفقات
     */
    openViewer(index) {
        this.currentViewerIndex = index;
        this.zoomLevel = 1;
        
        const modal = document.createElement('div');
        modal.className = 'modal fade attachment-viewer-modal';
        modal.innerHTML = this.createViewerHTML();
        
        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);
        
        this.setupViewerEvents(modal);
        this.loadAttachmentInViewer(modal);
        
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });

        bootstrapModal.show();
    }

    /**
     * إنشاء HTML عارض المرفقات
     */
    createViewerHTML() {
        const attachment = this.attachments[this.currentViewerIndex];
        
        return `
            <div class="modal-dialog modal-fullscreen">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-eye"></i> ${attachment.name}
                        </h5>
                        <div class="viewer-controls">
                            <button class="btn btn-sm btn-outline-light" id="prevAttachment" ${this.currentViewerIndex === 0 ? 'disabled' : ''}>
                                <i class="fas fa-chevron-right"></i>
                            </button>
                            <span class="attachment-counter">${this.currentViewerIndex + 1} من ${this.attachments.length}</span>
                            <button class="btn btn-sm btn-outline-light" id="nextAttachment" ${this.currentViewerIndex === this.attachments.length - 1 ? 'disabled' : ''}>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                        </div>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="viewer-container">
                            <div class="viewer-content" id="viewerContent">
                                <!-- سيتم تحميل المحتوى هنا -->
                            </div>
                            <div class="viewer-toolbar">
                                <div class="zoom-controls">
                                    <button class="btn btn-sm btn-outline-light" id="zoomOut">
                                        <i class="fas fa-search-minus"></i>
                                    </button>
                                    <span class="zoom-level" id="zoomLevel">100%</span>
                                    <button class="btn btn-sm btn-outline-light" id="zoomIn">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-light" id="resetZoom">
                                        <i class="fas fa-expand-arrows-alt"></i>
                                    </button>
                                </div>
                                <div class="viewer-actions">
                                    <button class="btn btn-sm btn-outline-light" id="rotateLeft">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-light" id="rotateRight">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-light" id="fullscreen">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-light" id="downloadCurrent">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * إعداد أحداث العارض
     */
    setupViewerEvents(modal) {
        // التنقل بين المرفقات
        modal.querySelector('#prevAttachment').addEventListener('click', () => {
            if (this.currentViewerIndex > 0) {
                this.currentViewerIndex--;
                this.updateViewer(modal);
            }
        });

        modal.querySelector('#nextAttachment').addEventListener('click', () => {
            if (this.currentViewerIndex < this.attachments.length - 1) {
                this.currentViewerIndex++;
                this.updateViewer(modal);
            }
        });

        // التحكم في التكبير
        modal.querySelector('#zoomIn').addEventListener('click', () => {
            this.zoomIn(modal);
        });

        modal.querySelector('#zoomOut').addEventListener('click', () => {
            this.zoomOut(modal);
        });

        modal.querySelector('#resetZoom').addEventListener('click', () => {
            this.resetZoom(modal);
        });

        // الدوران
        modal.querySelector('#rotateLeft').addEventListener('click', () => {
            this.rotateImage(modal, -90);
        });

        modal.querySelector('#rotateRight').addEventListener('click', () => {
            this.rotateImage(modal, 90);
        });

        // ملء الشاشة
        modal.querySelector('#fullscreen').addEventListener('click', () => {
            this.toggleFullscreen(modal);
        });

        // التحميل
        modal.querySelector('#downloadCurrent').addEventListener('click', () => {
            this.downloadCurrentAttachment();
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (modal.classList.contains('show')) {
                switch (e.key) {
                    case 'ArrowLeft':
                        modal.querySelector('#nextAttachment').click();
                        break;
                    case 'ArrowRight':
                        modal.querySelector('#prevAttachment').click();
                        break;
                    case '+':
                    case '=':
                        this.zoomIn(modal);
                        break;
                    case '-':
                        this.zoomOut(modal);
                        break;
                    case '0':
                        this.resetZoom(modal);
                        break;
                    case 'Escape':
                        bootstrap.Modal.getInstance(modal).hide();
                        break;
                }
            }
        });

        // السحب للتحريك
        this.setupPanAndZoom(modal);
    }

    /**
     * تحميل المرفق في العارض
     */
    loadAttachmentInViewer(modal) {
        const attachment = this.attachments[this.currentViewerIndex];
        const content = modal.querySelector('#viewerContent');
        
        if (this.isImageFile(attachment.file)) {
            content.innerHTML = `
                <img src="${attachment.dataURL}" 
                     class="viewer-image" 
                     id="viewerImage"
                     style="transform: scale(${this.zoomLevel})">
            `;
        } else if (this.isPDFFile(attachment.file)) {
            content.innerHTML = `
                <iframe src="${attachment.dataURL}" 
                        class="viewer-pdf" 
                        style="transform: scale(${this.zoomLevel})">
                </iframe>
            `;
        } else {
            content.innerHTML = `
                <div class="viewer-unsupported">
                    <i class="${this.getFileTypeIcon(attachment.type)} fa-4x mb-3"></i>
                    <h4>${attachment.name}</h4>
                    <p>لا يمكن معاينة هذا النوع من الملفات</p>
                    <button class="btn btn-primary" onclick="attachmentsManager.downloadAttachment('${attachment.id}')">
                        <i class="fas fa-download"></i> تحميل الملف
                    </button>
                </div>
            `;
        }
        
        this.updateZoomDisplay(modal);
    }

    /**
     * تحديث العارض
     */
    updateViewer(modal) {
        this.zoomLevel = 1;
        this.loadAttachmentInViewer(modal);
        
        // تحديث العنوان والعدادات
        const attachment = this.attachments[this.currentViewerIndex];
        modal.querySelector('.modal-title').innerHTML = `
            <i class="fas fa-eye"></i> ${attachment.name}
        `;
        modal.querySelector('.attachment-counter').textContent = 
            `${this.currentViewerIndex + 1} من ${this.attachments.length}`;
        
        // تحديث أزرار التنقل
        modal.querySelector('#prevAttachment').disabled = this.currentViewerIndex === 0;
        modal.querySelector('#nextAttachment').disabled = this.currentViewerIndex === this.attachments.length - 1;
    }

    /**
     * تكبير الصورة
     */
    zoomIn(modal) {
        if (this.zoomLevel < this.maxZoom) {
            this.zoomLevel += 0.25;
            this.applyZoom(modal);
        }
    }

    /**
     * تصغير الصورة
     */
    zoomOut(modal) {
        if (this.zoomLevel > this.minZoom) {
            this.zoomLevel -= 0.25;
            this.applyZoom(modal);
        }
    }

    /**
     * إعادة تعيين التكبير
     */
    resetZoom(modal) {
        this.zoomLevel = 1;
        this.applyZoom(modal);
    }

    /**
     * تطبيق التكبير
     */
    applyZoom(modal) {
        const image = modal.querySelector('#viewerImage');
        if (image) {
            image.style.transform = `scale(${this.zoomLevel})`;
        }
        this.updateZoomDisplay(modal);
    }

    /**
     * تحديث عرض مستوى التكبير
     */
    updateZoomDisplay(modal) {
        const zoomDisplay = modal.querySelector('#zoomLevel');
        if (zoomDisplay) {
            zoomDisplay.textContent = `${Math.round(this.zoomLevel * 100)}%`;
        }
    }

    /**
     * دوران الصورة
     */
    rotateImage(modal, degrees) {
        const image = modal.querySelector('#viewerImage');
        if (image) {
            const currentRotation = this.getCurrentRotation(image);
            const newRotation = currentRotation + degrees;
            image.style.transform = `scale(${this.zoomLevel}) rotate(${newRotation}deg)`;
        }
    }

    /**
     * الحصول على الدوران الحالي
     */
    getCurrentRotation(element) {
        const transform = element.style.transform;
        const match = transform.match(/rotate\(([^)]+)deg\)/);
        return match ? parseInt(match[1]) : 0;
    }

    /**
     * إعداد السحب والتكبير
     */
    setupPanAndZoom(modal) {
        const content = modal.querySelector('#viewerContent');
        let isDragging = false;
        let startX, startY, scrollLeft, scrollTop;

        content.addEventListener('mousedown', (e) => {
            if (e.target.tagName === 'IMG') {
                isDragging = true;
                startX = e.pageX - content.offsetLeft;
                startY = e.pageY - content.offsetTop;
                scrollLeft = content.scrollLeft;
                scrollTop = content.scrollTop;
                content.style.cursor = 'grabbing';
            }
        });

        content.addEventListener('mouseleave', () => {
            isDragging = false;
            content.style.cursor = 'default';
        });

        content.addEventListener('mouseup', () => {
            isDragging = false;
            content.style.cursor = 'default';
        });

        content.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            e.preventDefault();
            const x = e.pageX - content.offsetLeft;
            const y = e.pageY - content.offsetTop;
            const walkX = (x - startX) * 2;
            const walkY = (y - startY) * 2;
            content.scrollLeft = scrollLeft - walkX;
            content.scrollTop = scrollTop - walkY;
        });

        // التكبير بالعجلة
        content.addEventListener('wheel', (e) => {
            e.preventDefault();
            if (e.deltaY < 0) {
                this.zoomIn(modal);
            } else {
                this.zoomOut(modal);
            }
        });
    }

    /**
     * حذف مرفق
     */
    removeAttachment(attachmentId) {
        notificationManager.showConfirm(
            'هل أنت متأكد من حذف هذا المرفق؟',
            'تأكيد الحذف',
            () => {
                this.attachments = this.attachments.filter(a => a.id !== attachmentId);
                this.updateAttachmentsDisplay();
                notificationManager.showSuccess('تم حذف المرفق بنجاح');
            }
        );
    }

    /**
     * تحميل مرفق
     */
    downloadAttachment(attachmentId) {
        const attachment = this.attachments.find(a => a.id === attachmentId);
        if (!attachment) return;

        const url = attachment.dataURL || URL.createObjectURL(attachment.file);
        const a = document.createElement('a');
        a.href = url;
        a.download = attachment.name;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        if (!attachment.dataURL) {
            URL.revokeObjectURL(url);
        }
    }

    // الوظائف المساعدة
    isImageFile(file) {
        return file.type.startsWith('image/');
    }

    isPDFFile(file) {
        return file.type === 'application/pdf';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    truncateFileName(name, maxLength = 20) {
        if (name.length <= maxLength) return name;
        const extension = name.split('.').pop();
        const nameWithoutExt = name.substring(0, name.lastIndexOf('.'));
        const truncated = nameWithoutExt.substring(0, maxLength - extension.length - 4) + '...';
        return truncated + '.' + extension;
    }

    generateId() {
        return 'attachment_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
}

// إنشاء مثيل عام لإدارة المرفقات
window.attachmentsManager = new AttachmentsManager();
