/**
 * نظام المسح الضوئي للمستندات
 * يستخدم الماسح الضوئي الفعلي لمسح المستندات وحفظها كـ PDF
 */

class DocumentScanner {
    constructor() {
        this.DWObject = null;
        this.isInitialized = false;
        this.scannedImages = [];
        this.currentModal = null;
        this.scannerDevices = [];
        this.selectedDevice = null;
        this.scanSettings = {
            resolution: 200,
            colorMode: 'color', // color, grayscale, blackwhite
            pageSize: 'A4',
            duplex: false,
            autoFeeder: false
        };

        this.initializeTWAIN();
    }

    /**
     * تهيئة TWAIN للماسح الضوئي
     */
    initializeTWAIN() {
        // تحميل مكتبة Dynamic Web TWAIN
        if (typeof Dynamsoft === 'undefined') {
            this.loadTWAINLibrary();
        } else {
            this.setupTWAIN();
        }
    }

    /**
     * تحميل مكتبة TWAIN
     */
    loadTWAINLibrary() {
        // في التطبيق الحقيقي، يجب تحميل Dynamic Web TWAIN
        // هنا سنحاكي الوظائف الأساسية
        console.log('تحميل مكتبة TWAIN...');
        this.setupMockTWAIN();
    }

    /**
     * إعداد TWAIN وهمي للعرض التوضيحي
     */
    setupMockTWAIN() {
        // محاكاة أجهزة الماسح الضوئي
        this.scannerDevices = [
            { name: 'HP ScanJet Pro 2500', id: 'hp_2500' },
            { name: 'Canon CanoScan LiDE 400', id: 'canon_400' },
            { name: 'Epson Perfection V600', id: 'epson_v600' },
            { name: 'Brother MFC-L2750DW', id: 'brother_l2750' }
        ];
        this.isInitialized = true;
        console.log('تم تهيئة الماسح الضوئي بنجاح');
    }

    /**
     * فتح نافذة المسح الضوئي
     */
    openScanner() {
        if (!this.isInitialized) {
            notificationManager.showError('لم يتم تهيئة الماسح الضوئي بعد');
            return;
        }
        this.createScannerModal();
        this.loadAvailableScanners();
    }

    /**
     * إنشاء نافذة المسح الضوئي
     */
    createScannerModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fade scanner-modal';
        modal.id = 'scannerModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-print"></i> مسح المستندات ضوئياً
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="scanner-container">
                                    <div class="scanner-device-selection mb-3">
                                        <h6><i class="fas fa-scanner"></i> اختيار الماسح الضوئي</h6>
                                        <div class="row">
                                            <div class="col-md-8">
                                                <select class="form-control" id="scannerDeviceSelect">
                                                    <option value="">اختر الماسح الضوئي...</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <button class="btn btn-outline-primary w-100" id="refreshScannersBtn">
                                                    <i class="fas fa-sync-alt"></i> تحديث
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="scanner-preview" id="scannerPreview">
                                        <div class="preview-area">
                                            <div class="no-preview">
                                                <i class="fas fa-print fa-4x text-muted mb-3"></i>
                                                <h5>معاينة المسح الضوئي</h5>
                                                <p class="text-muted">اختر الماسح الضوئي وابدأ المسح لرؤية المعاينة هنا</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="scanner-controls mt-3">
                                        <button class="btn btn-primary btn-lg" id="startScanBtn" disabled>
                                            <i class="fas fa-play"></i> بدء المسح
                                        </button>
                                        <button class="btn btn-success btn-lg" id="scanPageBtn" disabled>
                                            <i class="fas fa-file-alt"></i> مسح صفحة
                                        </button>
                                        <button class="btn btn-warning btn-lg" id="previewScanBtn" disabled>
                                            <i class="fas fa-eye"></i> معاينة
                                        </button>
                                        <button class="btn btn-info btn-lg" id="scanSettingsBtn">
                                            <i class="fas fa-cog"></i> الإعدادات
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="scanned-pages">
                                    <h6><i class="fas fa-file-pdf"></i> الصفحات الممسوحة</h6>
                                    <div id="scannedPagesList" class="pages-list">
                                        <div class="no-pages">
                                            <i class="fas fa-file-pdf fa-2x text-muted"></i>
                                            <p class="text-muted">لم يتم مسح أي صفحات بعد</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="scan-info mt-3">
                                    <h6><i class="fas fa-info-circle"></i> معلومات المسح</h6>
                                    <div class="info-list">
                                        <div class="info-item">
                                            <span class="info-label">الدقة:</span>
                                            <span class="info-value" id="currentResolution">200 DPI</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">نمط الألوان:</span>
                                            <span class="info-value" id="currentColorMode">ملون</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">حجم الصفحة:</span>
                                            <span class="info-value" id="currentPageSize">A4</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">عدد الصفحات:</span>
                                            <span class="info-value" id="pageCount">0</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                        <button type="button" class="btn btn-danger" id="clearPagesBtn" disabled>
                            <i class="fas fa-trash"></i> مسح الكل
                        </button>
                        <button type="button" class="btn btn-success" id="savePDFBtn" disabled>
                            <i class="fas fa-file-pdf"></i> حفظ كـ PDF (<span id="pagesCount">0</span>)
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        this.currentModal = modal;

        const bootstrapModal = new bootstrap.Modal(modal);
        
        // إعداد الأحداث
        this.setupScannerEvents(modal);

        // تنظيف عند إغلاق النافذة
        modal.addEventListener('hidden.bs.modal', () => {
            this.cleanup();
            document.body.removeChild(modal);
        });

        bootstrapModal.show();
    }

    /**
     * إعداد أحداث المسح الضوئي
     */
    setupScannerEvents(modal) {
        // اختيار الماسح الضوئي
        modal.querySelector('#scannerDeviceSelect').addEventListener('change', (e) => {
            this.selectScanner(e.target.value);
        });

        // تحديث قائمة الماسحات
        modal.querySelector('#refreshScannersBtn').addEventListener('click', () => {
            this.loadAvailableScanners();
        });

        // بدء المسح
        modal.querySelector('#startScanBtn').addEventListener('click', () => {
            this.startScanning();
        });

        // مسح صفحة واحدة
        modal.querySelector('#scanPageBtn').addEventListener('click', () => {
            this.scanSinglePage();
        });

        // معاينة المسح
        modal.querySelector('#previewScanBtn').addEventListener('click', () => {
            this.previewScan();
        });

        // إعدادات المسح
        modal.querySelector('#scanSettingsBtn').addEventListener('click', () => {
            this.showScanSettings();
        });

        // مسح جميع الصفحات
        modal.querySelector('#clearPagesBtn').addEventListener('click', () => {
            this.clearAllPages();
        });

        // حفظ كـ PDF
        modal.querySelector('#savePDFBtn').addEventListener('click', () => {
            this.saveToPDF();
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (modal.classList.contains('show')) {
                if (e.code === 'F5') {
                    e.preventDefault();
                    this.scanSinglePage();
                } else if (e.code === 'F6') {
                    e.preventDefault();
                    this.previewScan();
                } else if (e.code === 'Escape') {
                    bootstrap.Modal.getInstance(modal).hide();
                }
            }
        });
    }

    /**
     * تحميل الماسحات الضوئية المتاحة
     */
    loadAvailableScanners() {
        const select = document.getElementById('scannerDeviceSelect');
        select.innerHTML = '<option value="">جاري البحث عن الماسحات...</option>';

        // محاكاة البحث عن الماسحات
        setTimeout(() => {
            select.innerHTML = '<option value="">اختر الماسح الضوئي...</option>';
            this.scannerDevices.forEach(device => {
                const option = document.createElement('option');
                option.value = device.id;
                option.textContent = device.name;
                select.appendChild(option);
            });

            if (this.scannerDevices.length === 0) {
                select.innerHTML = '<option value="">لم يتم العثور على ماسحات ضوئية</option>';
                notificationManager.showWarning('لم يتم العثور على ماسحات ضوئية متصلة');
            } else {
                notificationManager.showSuccess(`تم العثور على ${this.scannerDevices.length} ماسح ضوئي`);
            }
        }, 1000);
    }

    /**
     * اختيار الماسح الضوئي
     */
    selectScanner(deviceId) {
        if (!deviceId) {
            this.selectedDevice = null;
            this.disableScanButtons();
            return;
        }

        this.selectedDevice = this.scannerDevices.find(d => d.id === deviceId);
        if (this.selectedDevice) {
            this.enableScanButtons();
            notificationManager.showInfo(`تم اختيار: ${this.selectedDevice.name}`);
            this.updatePreviewArea();
        }
    }

    /**
     * تفعيل أزرار المسح
     */
    enableScanButtons() {
        document.getElementById('startScanBtn').disabled = false;
        document.getElementById('scanPageBtn').disabled = false;
        document.getElementById('previewScanBtn').disabled = false;
    }

    /**
     * تعطيل أزرار المسح
     */
    disableScanButtons() {
        document.getElementById('startScanBtn').disabled = true;
        document.getElementById('scanPageBtn').disabled = true;
        document.getElementById('previewScanBtn').disabled = true;
    }

    /**
     * تحديث منطقة المعاينة
     */
    updatePreviewArea() {
        const previewArea = document.querySelector('.preview-area');
        previewArea.innerHTML = `
            <div class="scanner-ready">
                <i class="fas fa-print fa-3x text-success mb-3"></i>
                <h5 class="text-success">الماسح الضوئي جاهز</h5>
                <p class="text-muted">${this.selectedDevice.name}</p>
                <div class="scanner-status">
                    <span class="badge bg-success">متصل</span>
                    <span class="badge bg-info">${this.scanSettings.resolution} DPI</span>
                    <span class="badge bg-secondary">${this.getColorModeText()}</span>
                </div>
            </div>
        `;
    }

    /**
     * عرض خطأ الكاميرا
     */
    showCameraError() {
        const cameraView = this.currentModal.querySelector('.camera-view');
        cameraView.innerHTML = `
            <div class="camera-error">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h5>لا يمكن الوصول للكاميرا</h5>
                <p class="text-muted">تأكد من:</p>
                <ul class="text-muted">
                    <li>منح صلاحية الوصول للكاميرا</li>
                    <li>عدم استخدام الكاميرا من تطبيق آخر</li>
                    <li>وجود كاميرا متصلة بالجهاز</li>
                </ul>
                <button class="btn btn-primary" onclick="scanner.initializeCamera()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }

    /**
     * التقاط صورة
     */
    captureImage() {
        if (!this.isScanning || !this.video.videoWidth) {
            notificationManager.showWarning('الكاميرا غير جاهزة');
            return;
        }

        try {
            // رسم الفيديو على الكانفاس
            this.context.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);
            
            // تطبيق التحسينات إذا كانت مفعلة
            if (document.getElementById('autoEnhance').checked) {
                this.enhanceImage();
            }

            // تحويل إلى صورة
            const quality = parseFloat(document.getElementById('imageQuality').value);
            const format = document.getElementById('imageFormat').value;
            const dataURL = this.canvas.toDataURL(format, quality);

            // إضافة الصورة للقائمة
            this.addCapturedImage(dataURL);

            // تأثير بصري للالتقاط
            this.showCaptureEffect();

            notificationManager.showSuccess('تم التقاط الصورة بنجاح');

        } catch (error) {
            console.error('خطأ في التقاط الصورة:', error);
            notificationManager.showError('فشل في التقاط الصورة');
        }
    }

    /**
     * تحسين الصورة تلقائياً
     */
    enhanceImage() {
        const imageData = this.context.getImageData(0, 0, this.canvas.width, this.canvas.height);
        const data = imageData.data;

        // تحسين التباين والسطوع
        for (let i = 0; i < data.length; i += 4) {
            // تحسين التباين
            data[i] = Math.min(255, Math.max(0, (data[i] - 128) * 1.2 + 128));     // أحمر
            data[i + 1] = Math.min(255, Math.max(0, (data[i + 1] - 128) * 1.2 + 128)); // أخضر
            data[i + 2] = Math.min(255, Math.max(0, (data[i + 2] - 128) * 1.2 + 128)); // أزرق
        }

        this.context.putImageData(imageData, 0, 0);
    }

    /**
     * إضافة صورة ملتقطة للقائمة
     */
    addCapturedImage(dataURL) {
        const imageId = 'img_' + Date.now();
        const imageObj = {
            id: imageId,
            dataURL: dataURL,
            timestamp: new Date().toISOString(),
            name: `مستند_${this.capturedImages.length + 1}`
        };

        this.capturedImages.push(imageObj);
        this.updateCapturedImagesList();
        this.updateSaveButton();
    }

    /**
     * تحديث قائمة الصور الملتقطة
     */
    updateCapturedImagesList() {
        const container = document.getElementById('capturedImagesList');
        
        if (this.capturedImages.length === 0) {
            container.innerHTML = `
                <div class="no-images">
                    <i class="fas fa-camera fa-2x text-muted"></i>
                    <p class="text-muted">لم يتم التقاط أي صور بعد</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.capturedImages.map(image => `
            <div class="captured-image-item" data-id="${image.id}">
                <img src="${image.dataURL}" alt="${image.name}" class="captured-thumbnail">
                <div class="image-info">
                    <small class="image-name">${image.name}</small>
                    <div class="image-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="scanner.previewImage('${image.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="scanner.removeImage('${image.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * تحديث زر الحفظ
     */
    updateSaveButton() {
        const saveBtn = document.getElementById('saveScannedBtn');
        const countSpan = document.getElementById('imageCount');
        
        countSpan.textContent = this.capturedImages.length;
        saveBtn.disabled = this.capturedImages.length === 0;
    }

    /**
     * معاينة صورة
     */
    previewImage(imageId) {
        const image = this.capturedImages.find(img => img.id === imageId);
        if (!image) return;

        const previewModal = document.createElement('div');
        previewModal.className = 'modal fade';
        previewModal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${image.name}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <img src="${image.dataURL}" class="img-fluid" alt="${image.name}">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(previewModal);
        const modal = new bootstrap.Modal(previewModal);
        
        previewModal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(previewModal);
        });

        modal.show();
    }

    /**
     * حذف صورة
     */
    removeImage(imageId) {
        this.capturedImages = this.capturedImages.filter(img => img.id !== imageId);
        this.updateCapturedImagesList();
        this.updateSaveButton();
        notificationManager.showInfo('تم حذف الصورة');
    }

    /**
     * تبديل الكاميرا
     */
    async switchCamera() {
        try {
            // إيقاف الكاميرا الحالية
            if (this.stream) {
                this.stream.getTracks().forEach(track => track.stop());
            }

            // تبديل بين الكاميرا الأمامية والخلفية
            const currentFacingMode = this.stream?.getVideoTracks()[0]?.getSettings()?.facingMode;
            const newFacingMode = currentFacingMode === 'user' ? 'environment' : 'user';

            const constraints = {
                video: {
                    facingMode: newFacingMode,
                    width: { ideal: 1920 },
                    height: { ideal: 1080 }
                }
            };

            this.stream = await navigator.mediaDevices.getUserMedia(constraints);
            this.video.srcObject = this.stream;

            notificationManager.showInfo('تم تبديل الكاميرا');

        } catch (error) {
            console.error('خطأ في تبديل الكاميرا:', error);
            notificationManager.showWarning('فشل في تبديل الكاميرا');
        }
    }

    /**
     * تبديل الفلاش
     */
    async toggleFlash() {
        try {
            const track = this.stream.getVideoTracks()[0];
            const capabilities = track.getCapabilities();

            if (capabilities.torch) {
                const settings = track.getSettings();
                await track.applyConstraints({
                    advanced: [{ torch: !settings.torch }]
                });
                
                const flashBtn = document.getElementById('toggleFlashBtn');
                flashBtn.classList.toggle('active');
                
                notificationManager.showInfo(settings.torch ? 'تم إطفاء الفلاش' : 'تم تشغيل الفلاش');
            } else {
                notificationManager.showWarning('الفلاش غير متوفر على هذا الجهاز');
            }

        } catch (error) {
            console.error('خطأ في تبديل الفلاش:', error);
            notificationManager.showWarning('فشل في تبديل الفلاش');
        }
    }

    /**
     * تأثير بصري للالتقاط
     */
    showCaptureEffect() {
        const overlay = this.currentModal.querySelector('.scanner-overlay');
        overlay.style.background = 'rgba(255, 255, 255, 0.8)';
        
        setTimeout(() => {
            overlay.style.background = 'transparent';
        }, 200);
    }

    /**
     * حفظ الصور الملتقطة
     */
    saveScannedImages() {
        if (this.capturedImages.length === 0) {
            notificationManager.showWarning('لا توجد صور لحفظها');
            return;
        }

        // إضافة الصور للمرفقات
        this.capturedImages.forEach(image => {
            const file = this.dataURLtoFile(image.dataURL, image.name + '.jpg');
            if (window.app && typeof window.app.handleFileUpload === 'function') {
                window.app.handleFileUpload([file]);
            }
        });

        notificationManager.showSuccess(`تم حفظ ${this.capturedImages.length} صورة بنجاح`);
        
        // إغلاق النافذة
        bootstrap.Modal.getInstance(this.currentModal).hide();
    }

    /**
     * تحويل DataURL إلى ملف
     */
    dataURLtoFile(dataURL, filename) {
        const arr = dataURL.split(',');
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }
        
        return new File([u8arr], filename, { type: mime });
    }

    /**
     * بدء المسح الضوئي المتعدد
     */
    startScanning() {
        if (!this.selectedDevice) {
            notificationManager.showError('يرجى اختيار الماسح الضوئي أولاً');
            return;
        }
        notificationManager.showInfo('بدء المسح الضوئي المتعدد...');
        this.simulateMultiPageScan();
    }

    /**
     * مسح صفحة واحدة
     */
    scanSinglePage() {
        if (!this.selectedDevice) {
            notificationManager.showError('يرجى اختيار الماسح الضوئي أولاً');
            return;
        }
        notificationManager.showInfo('جاري مسح الصفحة...');
        setTimeout(() => this.simulateScanPage(), 2000);
    }

    /**
     * محاكاة مسح صفحة
     */
    simulateScanPage() {
        const canvas = document.createElement('canvas');
        canvas.width = 2480;
        canvas.height = 3508;
        const ctx = canvas.getContext('2d');

        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        ctx.fillStyle = '#000000';
        ctx.font = '48px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('مستند ممسوح ضوئياً', canvas.width / 2, 200);
        ctx.fillText(`الصفحة ${this.scannedImages.length + 1}`, canvas.width / 2, 300);
        ctx.fillText(`${new Date().toLocaleString('ar-SA')}`, canvas.width / 2, 400);

        ctx.strokeStyle = '#cccccc';
        ctx.lineWidth = 4;
        ctx.strokeRect(50, 50, canvas.width - 100, canvas.height - 100);

        const imageData = canvas.toDataURL('image/jpeg', 0.9);

        const page = {
            id: Date.now(),
            data: imageData,
            timestamp: new Date(),
            pageNumber: this.scannedImages.length + 1
        };

        this.scannedImages.push(page);
        this.updateScannedPagesList();
        this.updatePageCount();

        notificationManager.showSuccess(`تم مسح الصفحة ${page.pageNumber} بنجاح`);
    }

    /**
     * محاكاة المسح المتعدد
     */
    simulateMultiPageScan() {
        let pageCount = 0;
        const maxPages = 3;

        const scanNextPage = () => {
            if (pageCount < maxPages) {
                pageCount++;
                setTimeout(() => {
                    this.simulateScanPage();
                    scanNextPage();
                }, 3000);
            } else {
                notificationManager.showSuccess(`تم الانتهاء من مسح ${maxPages} صفحات`);
            }
        };

        scanNextPage();
    }

    /**
     * معاينة المسح
     */
    previewScan() {
        if (!this.selectedDevice) {
            notificationManager.showError('يرجى اختيار الماسح الضوئي أولاً');
            return;
        }
        notificationManager.showInfo('جاري إنشاء معاينة...');
        setTimeout(() => this.showScanPreview(), 1500);
    }

    /**
     * عرض معاينة المسح
     */
    showScanPreview() {
        const previewArea = document.querySelector('.preview-area');
        previewArea.innerHTML = `
            <div class="scan-preview">
                <h6>معاينة المسح</h6>
                <div class="preview-image">
                    <div class="preview-placeholder">
                        <i class="fas fa-file-alt fa-3x text-info mb-2"></i>
                        <p>معاينة الصفحة التالية</p>
                        <small class="text-muted">${this.scanSettings.resolution} DPI - ${this.getColorModeText()}</small>
                    </div>
                </div>
            </div>
        `;
        notificationManager.showSuccess('تم إنشاء المعاينة');
    }

    /**
     * تحديث قائمة الصفحات الممسوحة
     */
    updateScannedPagesList() {
        const container = document.getElementById('scannedPagesList');

        if (this.scannedImages.length === 0) {
            container.innerHTML = `
                <div class="no-pages">
                    <i class="fas fa-file-pdf fa-2x text-muted"></i>
                    <p class="text-muted">لم يتم مسح أي صفحات بعد</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.scannedImages.map((page, index) => `
            <div class="scanned-page-item" data-page-id="${page.id}">
                <div class="page-thumbnail">
                    <img src="${page.data}" alt="صفحة ${page.pageNumber}">
                    <div class="page-number">${page.pageNumber}</div>
                </div>
                <div class="page-info">
                    <small class="text-muted">${page.timestamp.toLocaleTimeString('ar-SA')}</small>
                </div>
                <div class="page-actions">
                    <button class="btn btn-sm btn-outline-danger" onclick="scanner.removePage(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    /**
     * تحديث عداد الصفحات
     */
    updatePageCount() {
        document.getElementById('pageCount').textContent = this.scannedImages.length;
        document.getElementById('pagesCount').textContent = this.scannedImages.length;

        const hasPages = this.scannedImages.length > 0;
        document.getElementById('clearPagesBtn').disabled = !hasPages;
        document.getElementById('savePDFBtn').disabled = !hasPages;
    }

    /**
     * حذف صفحة
     */
    removePage(index) {
        if (index >= 0 && index < this.scannedImages.length) {
            this.scannedImages.splice(index, 1);
            this.scannedImages.forEach((page, i) => {
                page.pageNumber = i + 1;
            });
            this.updateScannedPagesList();
            this.updatePageCount();
            notificationManager.showSuccess('تم حذف الصفحة');
        }
    }

    /**
     * مسح جميع الصفحات
     */
    clearAllPages() {
        notificationManager.showConfirm(
            'هل أنت متأكد من حذف جميع الصفحات الممسوحة؟',
            'تأكيد الحذف',
            () => {
                this.scannedImages = [];
                this.updateScannedPagesList();
                this.updatePageCount();
                notificationManager.showSuccess('تم حذف جميع الصفحات');
            }
        );
    }

    /**
     * حفظ كـ PDF
     */
    saveToPDF() {
        if (this.scannedImages.length === 0) {
            notificationManager.showError('لا توجد صفحات للحفظ');
            return;
        }
        notificationManager.showInfo('جاري إنشاء ملف PDF...');
        setTimeout(() => this.generatePDF(), 2000);
    }

    /**
     * إنشاء ملف PDF
     */
    generatePDF() {
        try {
            // التحقق من وجود مكتبة jsPDF
            if (typeof window.jsPDF === 'undefined') {
                notificationManager.showError('مكتبة PDF غير متوفرة');
                return;
            }

            const { jsPDF } = window.jsPDF;
            const pdf = new jsPDF({
                orientation: 'portrait',
                unit: 'mm',
                format: 'a4'
            });

            // إضافة كل صفحة ممسوحة إلى PDF
            this.scannedImages.forEach((page, index) => {
                if (index > 0) {
                    pdf.addPage();
                }

                // إضافة الصورة إلى الصفحة
                const imgWidth = 190; // عرض الصورة في PDF (mm)
                const imgHeight = 270; // ارتفاع الصورة في PDF (mm)
                const x = (210 - imgWidth) / 2; // توسيط الصورة في صفحة A4
                const y = 10; // المسافة من أعلى الصفحة

                try {
                    pdf.addImage(page.data, 'JPEG', x, y, imgWidth, imgHeight);
                } catch (imgError) {
                    console.warn('خطأ في إضافة الصورة:', imgError);
                    // إضافة نص بديل في حالة فشل إضافة الصورة
                    pdf.setFontSize(16);
                    pdf.text(`صفحة ${page.pageNumber}`, 105, 150, { align: 'center' });
                    pdf.setFontSize(12);
                    pdf.text(`تاريخ المسح: ${page.timestamp.toLocaleString('ar-SA')}`, 105, 170, { align: 'center' });
                }

                // إضافة رقم الصفحة في الأسفل
                pdf.setFontSize(10);
                pdf.text(`${page.pageNumber}`, 105, 290, { align: 'center' });
            });

            // إضافة معلومات إضافية في الصفحة الأولى
            pdf.setPage(1);
            pdf.setFontSize(8);
            pdf.text(`تم الإنشاء: ${new Date().toLocaleString('ar-SA')}`, 10, 295);
            pdf.text(`عدد الصفحات: ${this.scannedImages.length}`, 10, 300);
            pdf.text(`الدقة: ${this.scanSettings.resolution} DPI`, 10, 305);

            // حفظ PDF
            const fileName = `مستند_ممسوح_${new Date().toISOString().split('T')[0]}.pdf`;
            const pdfBlob = pdf.output('blob');

            // تحميل الملف
            const url = URL.createObjectURL(pdfBlob);
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            // إضافة إلى نظام المرفقات
            if (window.attachmentsManager) {
                const file = new File([pdfBlob], fileName, { type: 'application/pdf' });
                attachmentsManager.addAttachment(file, URL.createObjectURL(pdfBlob));
            }

            notificationManager.showSuccess(`تم حفظ ${this.scannedImages.length} صفحة كـ PDF`);

            // إغلاق النافذة
            if (this.currentModal) {
                bootstrap.Modal.getInstance(this.currentModal).hide();
            }

        } catch (error) {
            console.error('خطأ في إنشاء PDF:', error);
            notificationManager.showError('حدث خطأ أثناء إنشاء PDF: ' + error.message);
        }
    }

    /**
     * عرض إعدادات المسح
     */
    showScanSettings() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-cog"></i> إعدادات المسح الضوئي
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group mb-3">
                            <label class="form-label">الدقة (DPI)</label>
                            <select class="form-control" id="scanResolution">
                                <option value="150">150 DPI - سريع</option>
                                <option value="200" selected>200 DPI - عادي</option>
                                <option value="300">300 DPI - عالي</option>
                                <option value="600">600 DPI - ممتاز</option>
                            </select>
                        </div>
                        <div class="form-group mb-3">
                            <label class="form-label">نمط الألوان</label>
                            <select class="form-control" id="scanColorMode">
                                <option value="color" selected>ملون</option>
                                <option value="grayscale">رمادي</option>
                                <option value="blackwhite">أبيض وأسود</option>
                            </select>
                        </div>
                        <div class="form-group mb-3">
                            <label class="form-label">حجم الصفحة</label>
                            <select class="form-control" id="scanPageSize">
                                <option value="A4" selected>A4</option>
                                <option value="A3">A3</option>
                                <option value="Letter">Letter</option>
                                <option value="Legal">Legal</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="scanner.applyScanSettings()">تطبيق</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);

        modal.querySelector('#scanResolution').value = this.scanSettings.resolution;
        modal.querySelector('#scanColorMode').value = this.scanSettings.colorMode;
        modal.querySelector('#scanPageSize').value = this.scanSettings.pageSize;

        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });

        bootstrapModal.show();
    }

    /**
     * تطبيق إعدادات المسح
     */
    applyScanSettings() {
        this.scanSettings.resolution = parseInt(document.getElementById('scanResolution').value);
        this.scanSettings.colorMode = document.getElementById('scanColorMode').value;
        this.scanSettings.pageSize = document.getElementById('scanPageSize').value;

        document.getElementById('currentResolution').textContent = this.scanSettings.resolution + ' DPI';
        document.getElementById('currentColorMode').textContent = this.getColorModeText();
        document.getElementById('currentPageSize').textContent = this.scanSettings.pageSize;

        const modal = document.querySelector('.modal.show');
        if (modal) {
            bootstrap.Modal.getInstance(modal).hide();
        }

        notificationManager.showSuccess('تم تحديث إعدادات المسح');
    }

    /**
     * الحصول على نص نمط الألوان
     */
    getColorModeText() {
        const modes = {
            'color': 'ملون',
            'grayscale': 'رمادي',
            'blackwhite': 'أبيض وأسود'
        };
        return modes[this.scanSettings.colorMode] || 'ملون';
    }

    /**
     * تنظيف الموارد
     */
    cleanup() {
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }

        this.video = null;
        this.canvas = null;
        this.context = null;
        this.isScanning = false;
        this.capturedImages = [];
        this.currentModal = null;
        this.scannedImages = [];
        this.selectedDevice = null;
    }
}

// إنشاء مثيل عام للماسح الضوئي
window.scanner = new DocumentScanner();
