/**
 * نظام المسح الضوئي للمستندات
 * يستخدم كاميرا الجهاز لمسح المستندات وتحويلها إلى صور
 */

class DocumentScanner {
    constructor() {
        this.stream = null;
        this.video = null;
        this.canvas = null;
        this.context = null;
        this.isScanning = false;
        this.capturedImages = [];
        this.currentModal = null;
    }

    /**
     * فتح نافذة المسح الضوئي
     */
    openScanner() {
        this.createScannerModal();
        this.initializeCamera();
    }

    /**
     * إنشاء نافذة المسح الضوئي
     */
    createScannerModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fade scanner-modal';
        modal.id = 'scannerModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-camera"></i> مسح المستندات ضوئياً
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="scanner-container">
                                    <div class="camera-view">
                                        <video id="scannerVideo" autoplay playsinline></video>
                                        <canvas id="scannerCanvas" style="display: none;"></canvas>
                                        <div class="scanner-overlay">
                                            <div class="scan-frame">
                                                <div class="corner top-left"></div>
                                                <div class="corner top-right"></div>
                                                <div class="corner bottom-left"></div>
                                                <div class="corner bottom-right"></div>
                                            </div>
                                        </div>
                                        <div class="scanner-instructions">
                                            <p><i class="fas fa-info-circle"></i> ضع المستند داخل الإطار واضغط على زر التقاط</p>
                                        </div>
                                    </div>
                                    <div class="scanner-controls">
                                        <button class="btn btn-primary btn-lg" id="captureBtn">
                                            <i class="fas fa-camera"></i> التقاط
                                        </button>
                                        <button class="btn btn-secondary" id="switchCameraBtn">
                                            <i class="fas fa-sync-alt"></i> تبديل الكاميرا
                                        </button>
                                        <button class="btn btn-info" id="toggleFlashBtn">
                                            <i class="fas fa-lightbulb"></i> الفلاش
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="captured-images">
                                    <h6><i class="fas fa-images"></i> الصور الملتقطة</h6>
                                    <div id="capturedImagesList" class="images-list">
                                        <div class="no-images">
                                            <i class="fas fa-camera fa-2x text-muted"></i>
                                            <p class="text-muted">لم يتم التقاط أي صور بعد</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="scan-settings mt-3">
                                    <h6><i class="fas fa-cog"></i> إعدادات المسح</h6>
                                    <div class="form-group mb-2">
                                        <label class="form-label">جودة الصورة</label>
                                        <select class="form-control form-control-sm" id="imageQuality">
                                            <option value="0.8">عادية</option>
                                            <option value="0.9" selected>عالية</option>
                                            <option value="1.0">ممتازة</option>
                                        </select>
                                    </div>
                                    <div class="form-group mb-2">
                                        <label class="form-label">تنسيق الملف</label>
                                        <select class="form-control form-control-sm" id="imageFormat">
                                            <option value="image/jpeg" selected>JPEG</option>
                                            <option value="image/png">PNG</option>
                                            <option value="image/webp">WebP</option>
                                        </select>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="autoEnhance" checked>
                                        <label class="form-check-label" for="autoEnhance">
                                            تحسين تلقائي
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                        <button type="button" class="btn btn-success" id="saveScannedBtn" disabled>
                            <i class="fas fa-save"></i> حفظ الصور (<span id="imageCount">0</span>)
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        this.currentModal = modal;

        const bootstrapModal = new bootstrap.Modal(modal);
        
        // إعداد الأحداث
        this.setupScannerEvents(modal);

        // تنظيف عند إغلاق النافذة
        modal.addEventListener('hidden.bs.modal', () => {
            this.cleanup();
            document.body.removeChild(modal);
        });

        bootstrapModal.show();
    }

    /**
     * إعداد أحداث المسح الضوئي
     */
    setupScannerEvents(modal) {
        // زر التقاط الصورة
        modal.querySelector('#captureBtn').addEventListener('click', () => {
            this.captureImage();
        });

        // زر تبديل الكاميرا
        modal.querySelector('#switchCameraBtn').addEventListener('click', () => {
            this.switchCamera();
        });

        // زر الفلاش
        modal.querySelector('#toggleFlashBtn').addEventListener('click', () => {
            this.toggleFlash();
        });

        // زر حفظ الصور
        modal.querySelector('#saveScannedBtn').addEventListener('click', () => {
            this.saveScannedImages();
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (modal.classList.contains('show')) {
                if (e.code === 'Space' || e.code === 'Enter') {
                    e.preventDefault();
                    this.captureImage();
                } else if (e.code === 'Escape') {
                    bootstrap.Modal.getInstance(modal).hide();
                }
            }
        });
    }

    /**
     * تهيئة الكاميرا
     */
    async initializeCamera() {
        try {
            this.video = document.getElementById('scannerVideo');
            this.canvas = document.getElementById('scannerCanvas');
            this.context = this.canvas.getContext('2d');

            // طلب الوصول للكاميرا
            const constraints = {
                video: {
                    facingMode: 'environment', // الكاميرا الخلفية
                    width: { ideal: 1920 },
                    height: { ideal: 1080 }
                }
            };

            this.stream = await navigator.mediaDevices.getUserMedia(constraints);
            this.video.srcObject = this.stream;
            
            this.video.addEventListener('loadedmetadata', () => {
                this.canvas.width = this.video.videoWidth;
                this.canvas.height = this.video.videoHeight;
            });

            this.isScanning = true;
            notificationManager.showSuccess('تم تشغيل الكاميرا بنجاح');

        } catch (error) {
            console.error('خطأ في تشغيل الكاميرا:', error);
            notificationManager.showError('فشل في الوصول للكاميرا. تأكد من منح الصلاحيات اللازمة.');
            this.showCameraError();
        }
    }

    /**
     * عرض خطأ الكاميرا
     */
    showCameraError() {
        const cameraView = this.currentModal.querySelector('.camera-view');
        cameraView.innerHTML = `
            <div class="camera-error">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h5>لا يمكن الوصول للكاميرا</h5>
                <p class="text-muted">تأكد من:</p>
                <ul class="text-muted">
                    <li>منح صلاحية الوصول للكاميرا</li>
                    <li>عدم استخدام الكاميرا من تطبيق آخر</li>
                    <li>وجود كاميرا متصلة بالجهاز</li>
                </ul>
                <button class="btn btn-primary" onclick="scanner.initializeCamera()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }

    /**
     * التقاط صورة
     */
    captureImage() {
        if (!this.isScanning || !this.video.videoWidth) {
            notificationManager.showWarning('الكاميرا غير جاهزة');
            return;
        }

        try {
            // رسم الفيديو على الكانفاس
            this.context.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);
            
            // تطبيق التحسينات إذا كانت مفعلة
            if (document.getElementById('autoEnhance').checked) {
                this.enhanceImage();
            }

            // تحويل إلى صورة
            const quality = parseFloat(document.getElementById('imageQuality').value);
            const format = document.getElementById('imageFormat').value;
            const dataURL = this.canvas.toDataURL(format, quality);

            // إضافة الصورة للقائمة
            this.addCapturedImage(dataURL);

            // تأثير بصري للالتقاط
            this.showCaptureEffect();

            notificationManager.showSuccess('تم التقاط الصورة بنجاح');

        } catch (error) {
            console.error('خطأ في التقاط الصورة:', error);
            notificationManager.showError('فشل في التقاط الصورة');
        }
    }

    /**
     * تحسين الصورة تلقائياً
     */
    enhanceImage() {
        const imageData = this.context.getImageData(0, 0, this.canvas.width, this.canvas.height);
        const data = imageData.data;

        // تحسين التباين والسطوع
        for (let i = 0; i < data.length; i += 4) {
            // تحسين التباين
            data[i] = Math.min(255, Math.max(0, (data[i] - 128) * 1.2 + 128));     // أحمر
            data[i + 1] = Math.min(255, Math.max(0, (data[i + 1] - 128) * 1.2 + 128)); // أخضر
            data[i + 2] = Math.min(255, Math.max(0, (data[i + 2] - 128) * 1.2 + 128)); // أزرق
        }

        this.context.putImageData(imageData, 0, 0);
    }

    /**
     * إضافة صورة ملتقطة للقائمة
     */
    addCapturedImage(dataURL) {
        const imageId = 'img_' + Date.now();
        const imageObj = {
            id: imageId,
            dataURL: dataURL,
            timestamp: new Date().toISOString(),
            name: `مستند_${this.capturedImages.length + 1}`
        };

        this.capturedImages.push(imageObj);
        this.updateCapturedImagesList();
        this.updateSaveButton();
    }

    /**
     * تحديث قائمة الصور الملتقطة
     */
    updateCapturedImagesList() {
        const container = document.getElementById('capturedImagesList');
        
        if (this.capturedImages.length === 0) {
            container.innerHTML = `
                <div class="no-images">
                    <i class="fas fa-camera fa-2x text-muted"></i>
                    <p class="text-muted">لم يتم التقاط أي صور بعد</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.capturedImages.map(image => `
            <div class="captured-image-item" data-id="${image.id}">
                <img src="${image.dataURL}" alt="${image.name}" class="captured-thumbnail">
                <div class="image-info">
                    <small class="image-name">${image.name}</small>
                    <div class="image-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="scanner.previewImage('${image.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="scanner.removeImage('${image.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * تحديث زر الحفظ
     */
    updateSaveButton() {
        const saveBtn = document.getElementById('saveScannedBtn');
        const countSpan = document.getElementById('imageCount');
        
        countSpan.textContent = this.capturedImages.length;
        saveBtn.disabled = this.capturedImages.length === 0;
    }

    /**
     * معاينة صورة
     */
    previewImage(imageId) {
        const image = this.capturedImages.find(img => img.id === imageId);
        if (!image) return;

        const previewModal = document.createElement('div');
        previewModal.className = 'modal fade';
        previewModal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${image.name}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <img src="${image.dataURL}" class="img-fluid" alt="${image.name}">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(previewModal);
        const modal = new bootstrap.Modal(previewModal);
        
        previewModal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(previewModal);
        });

        modal.show();
    }

    /**
     * حذف صورة
     */
    removeImage(imageId) {
        this.capturedImages = this.capturedImages.filter(img => img.id !== imageId);
        this.updateCapturedImagesList();
        this.updateSaveButton();
        notificationManager.showInfo('تم حذف الصورة');
    }

    /**
     * تبديل الكاميرا
     */
    async switchCamera() {
        try {
            // إيقاف الكاميرا الحالية
            if (this.stream) {
                this.stream.getTracks().forEach(track => track.stop());
            }

            // تبديل بين الكاميرا الأمامية والخلفية
            const currentFacingMode = this.stream?.getVideoTracks()[0]?.getSettings()?.facingMode;
            const newFacingMode = currentFacingMode === 'user' ? 'environment' : 'user';

            const constraints = {
                video: {
                    facingMode: newFacingMode,
                    width: { ideal: 1920 },
                    height: { ideal: 1080 }
                }
            };

            this.stream = await navigator.mediaDevices.getUserMedia(constraints);
            this.video.srcObject = this.stream;

            notificationManager.showInfo('تم تبديل الكاميرا');

        } catch (error) {
            console.error('خطأ في تبديل الكاميرا:', error);
            notificationManager.showWarning('فشل في تبديل الكاميرا');
        }
    }

    /**
     * تبديل الفلاش
     */
    async toggleFlash() {
        try {
            const track = this.stream.getVideoTracks()[0];
            const capabilities = track.getCapabilities();

            if (capabilities.torch) {
                const settings = track.getSettings();
                await track.applyConstraints({
                    advanced: [{ torch: !settings.torch }]
                });
                
                const flashBtn = document.getElementById('toggleFlashBtn');
                flashBtn.classList.toggle('active');
                
                notificationManager.showInfo(settings.torch ? 'تم إطفاء الفلاش' : 'تم تشغيل الفلاش');
            } else {
                notificationManager.showWarning('الفلاش غير متوفر على هذا الجهاز');
            }

        } catch (error) {
            console.error('خطأ في تبديل الفلاش:', error);
            notificationManager.showWarning('فشل في تبديل الفلاش');
        }
    }

    /**
     * تأثير بصري للالتقاط
     */
    showCaptureEffect() {
        const overlay = this.currentModal.querySelector('.scanner-overlay');
        overlay.style.background = 'rgba(255, 255, 255, 0.8)';
        
        setTimeout(() => {
            overlay.style.background = 'transparent';
        }, 200);
    }

    /**
     * حفظ الصور الملتقطة
     */
    saveScannedImages() {
        if (this.capturedImages.length === 0) {
            notificationManager.showWarning('لا توجد صور لحفظها');
            return;
        }

        // إضافة الصور للمرفقات
        this.capturedImages.forEach(image => {
            const file = this.dataURLtoFile(image.dataURL, image.name + '.jpg');
            if (window.app && typeof window.app.handleFileUpload === 'function') {
                window.app.handleFileUpload([file]);
            }
        });

        notificationManager.showSuccess(`تم حفظ ${this.capturedImages.length} صورة بنجاح`);
        
        // إغلاق النافذة
        bootstrap.Modal.getInstance(this.currentModal).hide();
    }

    /**
     * تحويل DataURL إلى ملف
     */
    dataURLtoFile(dataURL, filename) {
        const arr = dataURL.split(',');
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }
        
        return new File([u8arr], filename, { type: mime });
    }

    /**
     * تنظيف الموارد
     */
    cleanup() {
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }
        
        this.video = null;
        this.canvas = null;
        this.context = null;
        this.isScanning = false;
        this.capturedImages = [];
        this.currentModal = null;
    }
}

// إنشاء مثيل عام للماسح الضوئي
window.scanner = new DocumentScanner();
